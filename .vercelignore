# Development files
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
*.so
.pytest_cache/
.coverage
htmlcov/

# Virtual environments
venv/
env/
ENV/

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Documentation
README.md
docs/
*.md

# Test files
test_*.py
*_test.py
tests/

# Development scripts
create_tables.py
test_config_loading.py
force_reload_config.py

# Upload directories (will be created on server)
uploads/

# Log files
*.log

# Database files (using remote PostgreSQL)
*.db
*.sqlite
*.sqlite3

# Heavy dependencies (not needed for lightweight version)
app/core/invoice_generator.py

# Backup files
*.bak
*.backup

# Temporary files
*.tmp
*.temp
