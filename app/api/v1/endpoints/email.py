"""
Email management endpoints
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import Dict, Any
import logging

from app.db.database import get_db
from app.models.user import User as UserModel
from app.core.deps import require_admin
from app.core.email_service import email_service

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/test-email", summary="Test email configuration (Admin only)")
async def test_email_configuration(
    test_email: str,
    current_user: UserModel = Depends(require_admin)
) -> Dict[str, Any]:
    """
    Test email configuration by sending a test email (Admin only)
    
    **Parameters:**
    - `test_email`: Email address to send test email to
    
    **Returns:**
    - Email sending result and configuration status
    
    **Use Cases:**
    - Verify email configuration is working
    - Test SMTP settings
    - Validate email templates
    """
    try:
        logger.info(f"🧪 Admin testing email configuration - sending to {test_email}")
        
        # Send test email
        result = email_service.send_billing_confirmation_email(
            customer_email=test_email,
            customer_name="Test Customer",
            invoice_number="TEST-001",
            amount=100.00,
            job_name="Test Project",
            payment_url="https://example.com/payment"
        )
        
        return {
            "success": result["success"],
            "message": f"Test email result: {result['message']}",
            "email_sent_to": test_email,
            "smtp_configured": bool(email_service.password),
            "smtp_server": email_service.smtp_server,
            "smtp_port": email_service.smtp_port,
            "from_email": email_service.from_email
        }
        
    except Exception as e:
        logger.error(f"❌ Error testing email configuration: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error testing email configuration: {str(e)}"
        )


@router.get("/email-status", summary="Get email service status (Admin only)")
async def get_email_status(
    current_user: UserModel = Depends(require_admin)
) -> Dict[str, Any]:
    """
    Get email service configuration status (Admin only)
    
    **Returns:**
    - Email service configuration details
    - SMTP settings status
    - Email functionality availability
    
    **Use Cases:**
    - Check email service configuration
    - Verify SMTP settings
    - Monitor email functionality
    """
    try:
        return {
            "success": True,
            "email_configured": bool(email_service.password),
            "smtp_server": email_service.smtp_server,
            "smtp_port": email_service.smtp_port,
            "from_email": email_service.from_email,
            "from_name": email_service.from_name,
            "admin_email": email_service.admin_email,
            "use_tls": email_service.use_tls,
            "status": "Ready" if email_service.password else "Not Configured - Missing MAIL_PASSWORD"
        }
        
    except Exception as e:
        logger.error(f"❌ Error getting email status: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting email status: {str(e)}"
        )


@router.post("/send-payment-notification", summary="Manually send payment notification (Admin only)")
async def send_manual_payment_notification(
    billing_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_admin)
) -> Dict[str, Any]:
    """
    Manually send payment notification email to admin (Admin only)
    
    **Parameters:**
    - `billing_id`: ID of the billing record to send notification for
    
    **Returns:**
    - Email sending result
    
    **Use Cases:**
    - Resend payment notifications
    - Test payment notification emails
    - Manual notification triggers
    """
    try:
        from app.crud.billing import get_billing_by_id
        
        # Get billing record
        billing = get_billing_by_id(db, billing_id)
        if not billing:
            raise HTTPException(status_code=404, detail="Billing record not found")
        
        if not billing.paid:
            raise HTTPException(status_code=400, detail="Billing is not marked as paid")
        
        # Get job details
        job = billing.job
        
        # Determine payment method
        payment_method = "Unknown"
        if billing.payment_method_type:
            if "stripe_ach" in billing.payment_method_type:
                payment_method = "Stripe ACH (Bank Transfer)"
            elif "stripe_card" in billing.payment_method_type:
                payment_method = "Stripe Credit/Debit Card"
            elif "stripe_all" in billing.payment_method_type:
                payment_method = "Stripe (Card/ACH)"
            else:
                payment_method = billing.payment_method_type.replace("stripe_", "Stripe ").title()
        
        # Get customer email
        customer_email = "N/A"
        if billing.user:
            customer_email = billing.user.email
        
        logger.info(f"📧 Admin manually sending payment notification for billing {billing_id}")
        
        # Send payment notification email
        result = email_service.send_payment_notification_email(
            customer_name=billing.customer_name,
            invoice_number=billing.invoice_number,
            amount=float(billing.invoice_amount),
            job_name=job.job_name if job else f"Job #{billing.job_id}",
            payment_method=payment_method,
            customer_email=customer_email
        )
        
        return {
            "success": result["success"],
            "message": result["message"],
            "billing_id": billing_id,
            "invoice_number": billing.invoice_number,
            "customer_name": billing.customer_name,
            "amount": float(billing.invoice_amount)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Error sending manual payment notification: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error sending payment notification: {str(e)}"
        )
