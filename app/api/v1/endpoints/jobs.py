from fastapi import APIRouter, Depends, HTTPException, Query, UploadFile, File, Form
from fastapi.responses import FileResponse
import pandas as pd
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, timedelta
import io
import logging
from app.db.database import get_db
from app.schemas.job import Job, JobCreate, JobUpdate, JobList, DocumentInfo
from app.schemas.billing import BillingCreate, BillingUpdate
from app.models.job import JobStatus, ServiceType, Job as JobModel
from app.models.user import User as UserModel
from app.core.deps import get_current_active_user, require_admin
from app.core.file_upload import save_multiple_files, delete_job_documents, delete_file
from app.core.lightweight_invoice import create_job_invoice_in_memory
from app.core.stripe_service import stripe_service
from app.core.config import settings
from app.crud.job import (
    create_job,
    get_job_by_id,
    get_jobs,
    get_jobs_count,
    update_job,
    delete_job,
    get_jobs_by_client,
    update_job_status_on_payment
)
from app.crud.billing import create_billing, get_unpaid_billing_by_job_id, update_billing
from app.models.billing import Billing
from app.core.deltek_service import deltek_service
from app.core.scheduler import scheduler_service

# Configure logging
logger = logging.getLogger(__name__)

router = APIRouter()

def create_new_job(
    job: JobCreate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user)
):
    """
    Create a new job
    """
    return create_job(db=db, job=job)

@router.post("/create-and-upload", response_model=dict)
async def create_job_and_upload_files(
    job_data: str = Form(..., description="Job data as JSON string"),
    files: List[UploadFile] = File(None, description="Project documents to upload (optional)"),
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user)
):
    """
    Create a new job and upload files (Swagger UI compatible)

    This endpoint works better with Swagger UI for file uploads.

    **Job Data Format (JSON string):**
    ```json
    {
        "job_name": "Bridge Inspection",
        "service_type": "structural",
        "job_description": "Detailed description",
        "contract_term": "Contract terms",
        "contract_amount": "$50,000",
        "payment_id": "PAY-2024-001",
        "project_deadline": "2024-12-31",
        "preferred_contact_number": "+1-555-0123",
        "client_name": "ABC Construction Company"
    }
    ```

    **Service Types:**
    structural, mechanical, civil, electrical, forensic, other

    **Supported File Types:**
    PDF, DOC, DOCX, TXT, JPG, JPEG, PNG, GIF, XLS, XLSX, PPT, PPTX, ZIP, RAR, DWG, DXF

    **File Limits:**
    - Maximum 10 files per request
    - Maximum 10MB per file
    """
    import json

    # Parse job data from JSON string
    try:
        job_dict = json.loads(job_data)
    except json.JSONDecodeError:
        raise HTTPException(
            status_code=400,
            detail="Invalid JSON format in job_data parameter"
        )

    # Validate required fields
    if "job_name" not in job_dict or "service_type" not in job_dict:
        raise HTTPException(
            status_code=400,
            detail="job_name and service_type are required fields"
        )

    # Validate service_type
    try:
        service_type_enum = ServiceType(job_dict["service_type"])
    except ValueError:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid service_type. Must be one of: {[e.value for e in ServiceType]}"
        )

    # Parse project_deadline if provided
    deadline = None
    if job_dict.get("project_deadline"):
        try:
            from datetime import datetime
            deadline = datetime.strptime(job_dict["project_deadline"], "%Y-%m-%d").date()
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail="Invalid date format. Use YYYY-MM-DD"
            )

    # Create job data object
    job_create = JobCreate(
        job_name=job_dict["job_name"],
        job_description=job_dict.get("job_description"),
        contract_term=job_dict.get("contract_term"),
        contract_amount=job_dict.get("contract_amount"),
        payment_id=job_dict.get("payment_id"),
        service_type=service_type_enum,
        project_deadline=deadline,
        preferred_contact_number=job_dict.get("preferred_contact_number"),
        client_name=job_dict.get("client_name")
    )

    # Create the job first
    new_job = create_job(db=db, job=job_create)

    # Handle file uploads (optional)
    try:
        # Check if files were provided and filter out empty files
        if files:
            valid_files = [f for f in files if f.filename]

            if valid_files:
                file_infos = await save_multiple_files(valid_files)

                # Add timestamp to file info
                for file_info in file_infos:
                    file_info["uploaded_at"] = datetime.now().isoformat()

                # Update job with uploaded documents
                new_job.project_documents = file_infos
                db.commit()
                db.refresh(new_job)

    except Exception as e:
        # If file upload fails, delete the job and raise an error
        db.delete(new_job)
        db.commit()
        raise HTTPException(
            status_code=400,
            detail=f"Job creation failed due to file upload error: {str(e)}"
        )

    files_count = len(new_job.project_documents) if new_job.project_documents else 0
    message = f"Job created successfully" + (f" with {files_count} files" if files_count > 0 else "")

    return {
        "message": message,
        "job": {
            "id": new_job.id,
            "job_name": new_job.job_name,
            "service_type": new_job.service_type,
            "contract_amount": new_job.contract_amount,
            "files_uploaded": files_count
        },
        "uploaded_files": new_job.project_documents or []
    }

@router.get("/", response_model=JobList)
def read_jobs(
    skip: int = Query(0, ge=0, description="Number of jobs to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of jobs to return"),
    job_status: Optional[JobStatus] = Query(None, description="Filter by job status"),
    service_type: Optional[ServiceType] = Query(None, description="Filter by service type"),
    client_name: Optional[str] = Query(None, description="Filter by client name"),
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user)
):
    """
    Get jobs with optional filters (Admin only)

    **Features:**
    - Automatically checks for completed Stripe payments
    - Updates billing and job status in real-time
    - No webhooks required

    **Access Control:**
    - Admin users only
    """
    if current_user.role.value != "admin":
        raise HTTPException(
            status_code=403,
            detail="Not authorized to view jobs for other clients"
        )

    # Check for completed Stripe payments before returning jobs
    try:
        logger.info("🔍 Admin: Checking for completed Stripe payments before loading jobs...")
        payment_check_result = stripe_service.check_and_update_pending_payments(db)
        if payment_check_result["success"]:
            results = payment_check_result["results"]
            if results["newly_paid"] > 0:
                logger.info(f"✅ Admin: Found {results['newly_paid']} newly completed payments, {len(results['completed_jobs'])} jobs marked as completed")
        else:
            logger.warning(f"⚠️ Admin: Payment check failed: {payment_check_result.get('message', 'Unknown error')}")
    except Exception as e:
        logger.error(f"❌ Admin: Error during automatic payment check: {str(e)}")
        # Continue with job loading even if payment check fails

    jobs = get_jobs(
        db=db,
        skip=skip,
        limit=limit,
        client_name=client_name,
        job_status=job_status,
        service_type=service_type
    )
    total = get_jobs_count(
        db=db,
        client_name=client_name,
        job_status=job_status,
        service_type=service_type
    )

    return JobList(
        jobs=jobs,
        total=total,
        page=skip // limit + 1,
        size=len(jobs)
    )

@router.get("/by-client/{client_name}", response_model=List[Job])
def read_jobs_by_client(
    client_name: str,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user)
):
    """
    Get jobs for a specific client with automatic Stripe payment status updates

    **Parameters:**
    - `client_name`: Name of the client to filter jobs
    - `skip`: Number of records to skip (pagination)
    - `limit`: Maximum number of records to return

    **Features:**
    - Automatically checks for completed Stripe payments
    - Updates billing and job status in real-time
    - No webhooks required

    **Access Control:**
    - Users can only see their own jobs (client_name must match their full_name)
    - Admins can see any client's jobs

    **Returns:**
    - List of jobs for the specified client with updated payment status
    """
    # Check access control
    if current_user.full_name != client_name and current_user.role.value != "admin":
        return []

    # Check for completed Stripe payments before returning jobs
    try:
        logger.info(f"🔍 Client {client_name}: Checking for completed Stripe payments before loading jobs...")
        payment_check_result = stripe_service.check_and_update_pending_payments(db)
        if payment_check_result["success"]:
            results = payment_check_result["results"]
            if results["newly_paid"] > 0:
                logger.info(f"✅ Client {client_name}: Found {results['newly_paid']} newly completed payments, {len(results['completed_jobs'])} jobs marked as completed")
        else:
            logger.warning(f"⚠️ Client {client_name}: Payment check failed: {payment_check_result.get('message', 'Unknown error')}")
    except Exception as e:
        logger.error(f"❌ Client {client_name}: Error during automatic payment check: {str(e)}")
        # Continue with job loading even if payment check fails

    return get_jobs_by_client(
        db=db,
        client_name=client_name,
        skip=skip,
        limit=limit,
        update_stripe_status=True  # Always check Stripe status
    )

@router.post("/update-stripe-status")
def update_all_stripe_payment_status(
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user)
):
    """
    Bulk update Stripe payment status for all unpaid invoices
    Also updates job status to completed when payments are received

    This endpoint checks all unpaid billing records that have Stripe payment intent IDs
    and updates their payment status and job status based on current Stripe data.

    Returns:
        Summary of payment status and job status updates
    """
    from app.crud.billing import get_unpaid_billings_with_stripe, mark_billing_as_paid
    from app.crud.job import update_job_status_on_payment
    from app.models.job import JobStatus

    # Get all unpaid billings with Stripe payment intent IDs
    unpaid_billings = get_unpaid_billings_with_stripe(db)

    results = {
        "total_checked": len(unpaid_billings),
        "newly_paid": 0,
        "still_unpaid": 0,
        "errors": 0,
        "jobs_completed": 0,
        "updated_invoices": [],
        "completed_jobs": [],
        "error_details": []
    }

    # Track jobs that have payments updated
    jobs_with_payments = set()

    for billing in unpaid_billings:
        try:
            # Check Stripe payment status
            payment_status = stripe_service.get_payment_intent_status(billing.stripe_payment_intent_id)

            if payment_status["success"]:
                if payment_status["is_paid"]:
                    # Mark as paid
                    updated_billing = mark_billing_as_paid(db, billing.id)
                    if updated_billing:
                        results["newly_paid"] += 1
                        jobs_with_payments.add(billing.job_id)

                        results["updated_invoices"].append({
                            "billing_id": billing.id,
                            "invoice_number": billing.invoice_number,
                            "stripe_payment_intent_id": billing.stripe_payment_intent_id,
                            "job_id": billing.job_id,
                            "customer_name": billing.customer_name,
                            "amount": float(billing.invoice_amount),
                            "stripe_status": payment_status["status"]
                        })
                        print(f"✅ Marked billing {billing.id} as paid (Stripe: {billing.stripe_payment_intent_id})")
                else:
                    results["still_unpaid"] += 1
                    print(f"ℹ️  Billing {billing.id} still unpaid (Stripe status: {payment_status['status']})")
            else:
                results["errors"] += 1
                error_msg = f"Failed to check Stripe status for billing {billing.id}: {payment_status.get('error', 'Unknown error')}"
                results["error_details"].append(error_msg)
                print(f"❌ {error_msg}")

        except Exception as e:
            results["errors"] += 1
            error_msg = f"Exception checking billing {billing.id}: {str(e)}"
            results["error_details"].append(error_msg)
            print(f"❌ {error_msg}")

    # Update job statuses for jobs that received payments
    for job_id in jobs_with_payments:
        try:
            # Get the job to check current status and all billings
            job = get_job_by_id(db, job_id)
            if job and job.job_status != JobStatus.COMPLETED:
                print(f"🔍 Bulk update - Checking job {job_id} for completion:")

                # Get all billings for this job
                stripe_billings = [b for b in job.billings if b.stripe_payment_intent_id]
                all_billings = job.billings

                print(f"   Total billings: {len(all_billings)}")
                print(f"   Stripe billings: {len(stripe_billings)}")
                print(f"   Current job status: {job.job_status}")

                for billing in all_billings:
                    status = "PAID" if billing.paid else "UNPAID"
                    stripe_info = f" (Stripe: {billing.stripe_payment_intent_id})" if billing.stripe_payment_intent_id else " (No Stripe)"
                    print(f"   Billing {billing.id}: {status}{stripe_info}")

                # Check if all Stripe billings are paid
                if stripe_billings:
                    all_stripe_billings_paid = all(billing.paid for billing in stripe_billings)
                    print(f"   All Stripe billings paid: {all_stripe_billings_paid}")

                    if all_stripe_billings_paid:
                        print(f"   🎯 Bulk update - Attempting to mark job {job_id} as COMPLETED...")
                        updated_job = update_job_status_on_payment(db, job_id, JobStatus.COMPLETED)
                        if updated_job:
                            results["jobs_completed"] += 1
                            results["completed_jobs"].append({
                                "job_id": job_id,
                                "job_name": job.job_name,
                                "client_name": job.client_name,
                                "previous_status": job.job_status.value if hasattr(job.job_status, 'value') else str(job.job_status),
                                "new_status": "completed"
                            })
                            print(f"🎉 Bulk update - Job {job_id} marked as COMPLETED due to payment received")
                        else:
                            print(f"❌ Bulk update - Failed to update job {job_id} status to COMPLETED")
                    else:
                        print(f"   ⏳ Bulk update - Job {job_id} not completed - some Stripe billings still unpaid")
                else:
                    print(f"   ℹ️  Bulk update - Job {job_id} has no Stripe billings - skipping auto-completion")
            else:
                if job:
                    print(f"   ℹ️  Bulk update - Job {job_id} already completed (status: {job.job_status})")
                else:
                    print(f"   ❌ Bulk update - Job {job_id} not found")

        except Exception as e:
            error_msg = f"Exception updating job status for job {job_id}: {str(e)}"
            results["error_details"].append(error_msg)
            print(f"❌ {error_msg}")

    return {
        "success": True,
        "message": f"Stripe status update completed. {results['newly_paid']} invoices marked as paid, {results['jobs_completed']} jobs marked as completed.",
        "results": results
    }

@router.get("/{job_id}", response_model=Job)
def read_job(
    job_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user)
):
    """
    Get job by ID
    """
    job = get_job_by_id(db=db, job_id=job_id)
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")

    # Check if user can access this job
    # Admin users can access any job, regular users can only access jobs assigned to them
    if current_user.role.value != "admin" and job.client_name != current_user.full_name:
        raise HTTPException(
            status_code=403,
            detail="Not authorized to access this job"
        )

    return job

@router.put("/{job_id}", response_model=Job)
def update_existing_job(
    job_id: int,
    job_update: JobUpdate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user)
):
    """
    Update job
    """
    # First get the job to check access
    job = get_job_by_id(db=db, job_id=job_id)
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")

    # Check if user can access this job
    if current_user.role.value != "admin" and job.client_name != current_user.full_name:
        raise HTTPException(
            status_code=403,
            detail="Not authorized to update this job"
        )

    updated_job = update_job(db=db, job_id=job_id, job_update=job_update)
    return updated_job

@router.delete("/{job_id}")
def delete_existing_job(
    job_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user)
):
    """
    Delete job
    """
    # First get the job to check access
    job = get_job_by_id(db=db, job_id=job_id)
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")

    # Check if user can access this job
    if current_user.role.value != "admin" and job.client_name != current_user.full_name:
        raise HTTPException(
            status_code=403,
            detail="Not authorized to delete this job"
        )

    success = delete_job(db=db, job_id=job_id)
    if not success:
        raise HTTPException(status_code=404, detail="Job not found")
    return {"message": "Job deleted successfully"}

@router.get("/status/options")
def get_job_status_options():
    """
    Get available job status options
    """
    return {"job_statuses": [status.value for status in JobStatus]}

@router.get("/service-type/options")
def get_service_type_options():
    """
    Get available service type options
    """
    return {"service_types": [service_type.value for service_type in ServiceType]}

@router.post("/test-file-upload")
async def test_file_upload(
    files: List[UploadFile] = File(..., description="Test files for upload")
):
    """
    Test file upload functionality (Swagger UI compatible)

    This is a simple test endpoint to verify file uploads work in Swagger UI.
    It doesn't save files, just returns information about uploaded files.

    **Usage:**
    1. Click "Choose Files" in Swagger UI
    2. Select one or more files
    3. Click "Execute"
    4. Check the response for file information
    """
    file_info = []
    for file in files:
        if file.filename:
            content = await file.read()
            file_info.append({
                "filename": file.filename,
                "content_type": file.content_type,
                "size": len(content)
            })

    return {
        "message": "File upload test successful!",
        "files_received": len(file_info),
        "file_details": file_info
    }

@router.post("/{job_id}/upload-documents", summary="Upload documents to job")
async def upload_job_documents(
    job_id: int,
    files: List[UploadFile] = File(..., description="Documents to upload (max 10 files, 10MB each)"),
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user)
):
    """
    Upload documents to an existing job (Swagger UI compatible)

    This endpoint works reliably in Swagger UI for file uploads.
    Simply select the files and click execute.

    **Supported File Types:**
    PDF, DOC, DOCX, TXT, JPG, JPEG, PNG, GIF, XLS, XLSX, PPT, PPTX, ZIP, RAR, DWG, DXF

    **File Limits:**
    - Maximum 10 files per request
    - Maximum 10MB per file

    **Permissions:**
    - Job creator can upload documents
    - Admins can upload documents to any job

    **Usage in Swagger UI:**
    1. Enter the job_id
    2. Click "Choose Files" and select your documents
    3. Click "Execute"
    """
    # Get the job and check access
    job = get_job_by_id(db=db, job_id=job_id)
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")

    # Check if user can access this job
    if current_user.role != "admin" and job.client_name != current_user.full_name:
        raise HTTPException(
            status_code=403,
            detail="Not authorized to upload documents to this job"
        )

    # Save uploaded files
    try:
        file_infos = await save_multiple_files(files)

        # Add timestamp to file info
        for file_info in file_infos:
            file_info["uploaded_at"] = datetime.now().isoformat()

        # Update job with new documents (append to existing)
        existing_docs = job.project_documents or []
        updated_docs = existing_docs + file_infos
        job.project_documents = updated_docs

        db.commit()
        db.refresh(job)

        return {
            "message": f"Successfully uploaded {len(file_infos)} documents",
            "uploaded_files": file_infos,
            "job_id": job_id
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to upload files: {str(e)}")

@router.get("/{job_id}/documents")
def get_job_documents(
    job_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user)
):
    """
    Get list of documents for a job
    """
    job = get_job_by_id(db=db, job_id=job_id)
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")

    # Check if user can access this job
    if current_user.role != "admin" and job.client_name != current_user.full_name:
        raise HTTPException(
            status_code=403,
            detail="Not authorized to access documents for this job"
        )

    return {
        "job_id": job_id,
        "documents": job.project_documents or []
    }

@router.get("/{job_id}/documents/{document_index}/download")
async def download_job_document(
    job_id: int,
    document_index: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user)
):
    """
    Download a specific document from a job
    """
    job = get_job_by_id(db=db, job_id=job_id)
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")

    # Check if user can access this job
    if current_user.role != "admin" and job.client_name != current_user.full_name:
        raise HTTPException(
            status_code=403,
            detail="Not authorized to download documents from this job"
        )

    documents = job.project_documents or []
    if document_index >= len(documents) or document_index < 0:
        raise HTTPException(status_code=404, detail="Document not found")

    document = documents[document_index]
    file_path = document.get("file_path")
    original_filename = document.get("original_filename")

    if not file_path or not original_filename:
        raise HTTPException(status_code=404, detail="Document file not found")

    return FileResponse(
        path=file_path,
        filename=original_filename,
        media_type='application/octet-stream'
    )

@router.delete("/{job_id}/documents/{document_index}")
def delete_job_document(
    job_id: int,
    document_index: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user)
):
    """
    Delete a specific document from a job
    """
    job = get_job_by_id(db=db, job_id=job_id)
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")

    # Check if user can access this job
    if current_user.role != "admin" and job.client_name != current_user.full_name:
        raise HTTPException(
            status_code=403,
            detail="Not authorized to delete documents from this job"
        )



    documents = job.project_documents or []
    if document_index >= len(documents) or document_index < 0:
        raise HTTPException(status_code=404, detail="Document not found")

    # Delete the file from storage
    document_to_delete = documents[document_index]
    file_path = document_to_delete.get("file_path")
    if file_path:
        delete_file(file_path)

    # Remove from database
    documents.pop(document_index)
    job.project_documents = documents

    db.commit()

    return {"message": "Document deleted successfully"}

def parse_amount(value):
    """Parse amount string to float, handling commas and invalid values"""
    try:
        if pd.isna(value) or value == "":
            return None
        return float(str(value).replace(",", "").strip())
    except:
        return None

def parse_date(value):
    """Parse date string to date object"""
    try:
        if pd.isna(value) or value == "":
            return None
        # Try different date formats
        for fmt in ["%m/%d/%Y", "%Y-%m-%d", "%d/%m/%Y"]:
            try:
                return datetime.strptime(str(value), fmt).date()
            except ValueError:
                continue
        return None
    except:
        return None

@router.post("/import-csv", summary="Import jobs from CSV file")
async def import_jobs_from_csv(
    file: UploadFile = File(..., description="CSV file to import jobs from"),
    skip_duplicates: bool = Query(True, description="Skip jobs that already exist"),
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_admin)
):
    """
    Import jobs from a CSV file (Admin only)

    **Required CSV Columns:**
    - `groupHeader_BillingGroupDetails` or `job_name`: Job name (required)
    - `detail_ClientName` or `client_name`: Client name
    - `description` or `job_description`: Job description
    - `detail_DueDate` or `project_deadline`: Project deadline (MM/DD/YYYY format)
    - `detail_OriginalAmt` or `contract_amount`: Contract amount

    **Optional Parameters:**
    - `skip_duplicates`: If true, skip jobs with names that already exist

    **File Requirements:**
    - Must be a CSV file
    - Maximum file size: 10MB
    - Encoding: UTF-8 or ISO-8859-1

    **Date Formats Supported:**
    - MM/DD/YYYY
    - YYYY-MM-DD
    - DD/MM/YYYY

    **Amount Format:**
    - Numbers with or without commas
    - Currency symbols will be preserved as strings

    **Example CSV Structure:**
    ```
    groupHeader_BillingGroupDetails,detail_ClientName,description,detail_DueDate,detail_OriginalAmt
    "Project ABC",ABC Corp,"Building inspection","12/31/2024","5000.00"
    "Project XYZ",XYZ Inc,"Structural analysis","01/15/2025","7500"
    ```
    """
    if current_user.role.value != "admin":
        raise HTTPException(
            status_code=403,
            detail="Only admin users can import CSV files"
        )
    # Validate file type
    if not file.filename.lower().endswith('.csv'):
        raise HTTPException(
            status_code=400,
            detail="File must be a CSV file"
        )

    # Read file content
    try:
        content = await file.read()

        # Try different encodings
        for encoding in ['utf-8', 'iso-8859-1', 'cp1252']:
            try:
                csv_content = content.decode(encoding)
                df = pd.read_csv(io.StringIO(csv_content))
                break
            except (UnicodeDecodeError, pd.errors.EmptyDataError):
                continue
        else:
            raise HTTPException(
                status_code=400,
                detail="Unable to read CSV file. Please check the file encoding."
            )

    except Exception as e:
        raise HTTPException(
            status_code=400,
            detail=f"Error reading CSV file: {str(e)}"
        )

    # Define column mappings (flexible to handle different column names)
    column_mapping = {
        "groupHeader_BillingGroupDetails": "job_name",
        "job_name": "job_name",
        "detail_ClientName": "client_name",
        "client_name": "client_name",
        "description": "job_description",
        "job_description": "job_description",
        "detail_DueDate": "project_deadline",
        "project_deadline": "project_deadline",
        "detail_OriginalAmt": "contract_amount",
        "contract_amount": "contract_amount"
    }

    # Find available columns and map them
    available_columns = []
    mapped_columns = {}

    for col in df.columns:
        if col in column_mapping:
            mapped_columns[col] = column_mapping[col]
            available_columns.append(col)

    # Check if we have at least job_name
    job_name_cols = [col for col in available_columns if column_mapping[col] == "job_name"]
    if not job_name_cols:
        raise HTTPException(
            status_code=400,
            detail="CSV must contain a job name column (groupHeader_BillingGroupDetails or job_name)"
        )

    # Filter and rename columns
    filtered_df = df[available_columns].rename(columns=mapped_columns)

    # Process data
    if 'contract_amount' in filtered_df.columns:
        filtered_df["contract_amount"] = filtered_df["contract_amount"].apply(parse_amount)

    if 'project_deadline' in filtered_df.columns:
        filtered_df["project_deadline"] = filtered_df["project_deadline"].apply(parse_date)

    # Remove duplicates within the CSV by job_name, prioritizing records with contract_amount values
    if 'contract_amount' in filtered_df.columns:
        filtered_df = filtered_df.sort_values(['job_name', 'contract_amount'], na_position='last').drop_duplicates(subset='job_name', keep='first')
    else:
        filtered_df = filtered_df.drop_duplicates(subset='job_name', keep='first')

    # Remove rows with empty job_name
    filtered_df = filtered_df.dropna(subset=['job_name'])
    filtered_df = filtered_df[filtered_df['job_name'].str.strip() != '']

    # Import to database
    imported_count = 0
    skipped_count = 0
    errors = []

    try:
        for index, row in filtered_df.iterrows():
            try:
                job_name = str(row['job_name']).strip()

                # Check if job already exists
                if skip_duplicates:
                    existing_job = db.query(JobModel).filter(JobModel.job_name == job_name).first()
                    if existing_job:
                        skipped_count += 1
                        continue

                # Create new job using the create_job function to ensure proper handling
                job_create_data = JobCreate(
                    job_name=job_name,
                    client_name=str(row.get('client_name', '')).strip() if pd.notna(row.get('client_name')) else None,
                    job_description=str(row.get('job_description', '')).strip() if pd.notna(row.get('job_description')) else None,
                    project_deadline=row.get('project_deadline'),
                    contract_amount=str(row['contract_amount']) if pd.notna(row.get('contract_amount')) else None,
                    service_type=None  # Will default to None, which is allowed
                )

                # Use the existing create_job function to ensure proper creation
                new_job = create_job(db=db, job=job_create_data)
                imported_count += 1

            except Exception as e:
                errors.append(f"Row {index + 1}: {str(e)}")
                continue

        # Note: create_job function handles commit internally, so no need to commit here

        return {
            "message": "CSV import completed successfully",
            "summary": {
                "total_rows_processed": len(filtered_df),
                "imported": imported_count,
                "skipped": skipped_count,
                "errors": len(errors)
            },
            "details": {
                "columns_found": list(mapped_columns.keys()),
                "columns_mapped": mapped_columns,
                "errors": errors[:10] if errors else []  # Show first 10 errors
            }
        }

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Database error during import: {str(e)}"
        )

@router.get("/{job_id}/generate-invoice", summary="Generate and download invoice for job")
async def generate_job_invoice(
    job_id: int,
    customer_email: Optional[str] = Query(None, description="Customer email for Stripe invoice (optional)"),
    create_stripe_payment: bool = Query(False, description="Create Stripe ACH payment and include payment link"),
    payment_method: str = Query("ach", description="Payment method: 'ach', 'card', or 'all'"),
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user)
):
    """
    Generate and download an HTML invoice for a specific job with optional Stripe payment integration

    **Parameters:**
    - `job_id`: ID of the job to generate invoice for
    - `customer_email`: Customer email for Stripe payment (required if create_stripe_payment=true)
    - `create_stripe_payment`: Whether to create Stripe payment and include payment link in HTML
    - `payment_method`: Payment method type ('ach', 'card', or 'all')

    **Returns:**
    - HTML file download (text/html)

    **Invoice Details:**
    - Company: DOTec Corp. information
    - Client: Job client name
    - Project: Job name/description
    - Amount: Contract amount from job (no tax added)
    - Due Date: Project deadline or 30 days from invoice date
    - Invoice Number: Auto-generated based on job ID and timestamp
    - Payment Link: Stripe payment link (if Stripe payment created)

    **Stripe Payment Integration (when create_stripe_payment=true):**
    - **ACH (payment_method='ach')**: Bank transfer payments (lower fees, 3-5 business days)
    - **Card (payment_method='card')**: Credit/debit card payments (instant, higher fees)
    - **All (payment_method='all')**: Customer can choose between ACH or card
    - Creates Stripe checkout session for selected payment method(s)
    - Provides hosted payment page with secure payment collection
    - Embeds Stripe payment link in HTML
    - Stores Stripe payment intent ID in billing record
    - Enables automatic payment status updates via webhooks

    **Features:**
    - Generated on-the-fly (not saved to disk)
    - Professional HTML format with integrated Stripe payments
    - Auto-generated invoice numbers
    - Clickable payment links with multiple payment options
    - Smart billing: Reuses unpaid invoices instead of creating duplicates
    - Flexible payment methods to suit customer preferences

    **Permissions:**
    - Job creator can generate invoices
    - Admins can generate invoices for any job
    """

    # Get the job
    job = get_job_by_id(db=db, job_id=job_id)
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")

    # Check if user can access this job
    if current_user.role.value != "admin" and job.client_name != current_user.full_name:
        raise HTTPException(
            status_code=403,
            detail="Not authorized to generate invoice for this job"
        )

    # Prepare invoice data
    client_name = job.client_name or "Client"
    job_name = job.job_name
    project_deadline = job.project_deadline.strftime('%Y-%m-%d') if job.project_deadline else None

    # If no deadline, set due date to 30 days from now
    if not project_deadline:
        from datetime import timedelta
        due_date = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')
    else:
        due_date = project_deadline

    # Parse contract amount
    contract_amount = 0.0
    if job.contract_amount:
        try:
            # Remove currency symbols and commas, then convert to float
            amount_str = str(job.contract_amount).replace('$', '').replace(',', '').strip()
            contract_amount = float(amount_str)
        except (ValueError, TypeError):
            contract_amount = 0.0

    try:
        # Stripe checkout URL for HTML integration
        stripe_checkout_url = None
        stripe_payment_intent_id = None

        # Check if Stripe payment should be created
        if create_stripe_payment:
            if not customer_email:
                raise HTTPException(
                    status_code=400,
                    detail="Customer email is required when creating Stripe payment"
                )

            # First, check if a Stripe payment already exists for this job
            existing_billing = get_unpaid_billing_by_job_id(db=db, job_id=job_id)

            if existing_billing and existing_billing.stripe_payment_intent_id:
                # Use existing Stripe payment
                print(f"Using existing Stripe payment: {existing_billing.stripe_payment_intent_id}")
                stripe_payment_intent_id = existing_billing.stripe_payment_intent_id

                # For existing payments, we need to construct the checkout URL
                # In a real implementation, you'd store the checkout session ID or URL
                stripe_checkout_url = f"https://checkout.stripe.com/pay/cs_existing#{stripe_payment_intent_id}"
                print(f"Using existing Stripe checkout URL: {stripe_checkout_url}")
            else:
                # Create new Stripe checkout session
                print(f"Creating new Stripe checkout session for {payment_method} payment...")

                # Generate invoice number for Stripe
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                invoice_number = f"INV-{job_id}-{timestamp}"

                # Set success and cancel URLs
                base_url = settings.BASE_URL
                success_url = f"{base_url}/api/v1/public/payment-success?session_id={{CHECKOUT_SESSION_ID}}"
                cancel_url = f"{base_url}/api/v1/jobs/payment-cancelled"

                # Create Stripe checkout session based on payment method
                if payment_method == "card":
                    stripe_result = stripe_service.create_checkout_session_for_cards(
                        amount=contract_amount,
                        customer_email=customer_email,
                        customer_name=client_name,
                        invoice_number=invoice_number,
                        job_name=job_name,
                        success_url=success_url,
                        cancel_url=cancel_url
                    )
                elif payment_method == "all":
                    stripe_result = stripe_service.create_checkout_session_for_all_methods(
                        amount=contract_amount,
                        customer_email=customer_email,
                        customer_name=client_name,
                        invoice_number=invoice_number,
                        job_name=job_name,
                        success_url=success_url,
                        cancel_url=cancel_url
                    )
                else:  # Default to ACH
                    stripe_result = stripe_service.create_checkout_session_for_ach(
                        amount=contract_amount,
                        customer_email=customer_email,
                        customer_name=client_name,
                        invoice_number=invoice_number,
                        job_name=job_name,
                        success_url=success_url,
                        cancel_url=cancel_url
                    )

                if not stripe_result["success"]:
                    raise HTTPException(
                        status_code=500,
                        detail=f"Failed to create Stripe checkout session: {stripe_result.get('message', 'Unknown error')}"
                    )

                stripe_checkout_url = stripe_result["checkout_url"]
                stripe_payment_intent_id = stripe_result["payment_intent_id"]
                print(f"Created new Stripe checkout session: {stripe_result['checkout_session_id']}")

        # Handle billing record creation/update
        if create_stripe_payment:
            # When creating Stripe payment, we already checked for existing billing above
            existing_unpaid_billing = get_unpaid_billing_by_job_id(db=db, job_id=job_id)

            if existing_unpaid_billing and existing_unpaid_billing.stripe_payment_intent_id:
                # Use existing billing record with Stripe payment
                billing_record = existing_unpaid_billing
                invoice_number = billing_record.invoice_number
                print(f"Using existing billing with Stripe: {invoice_number}")
            elif existing_unpaid_billing:
                # Update existing billing record with new Stripe info
                payment_method_type = f"stripe_{payment_method}"
                billing_update = BillingUpdate(
                    stripe_payment_intent_id=stripe_payment_intent_id,
                    stripe_checkout_session_id=stripe_result.get("checkout_session_id"),
                    payment_method_type=payment_method_type
                )
                billing_record = update_billing(db=db, billing_id=existing_unpaid_billing.id, billing_update=billing_update)
                invoice_number = billing_record.invoice_number
                print(f"Updated existing billing with Stripe {payment_method}: {invoice_number}")
            else:
                # Create new billing record with Stripe info
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                invoice_number = f"INV-{job_id}-{timestamp}"

                # Parse due date for billing record
                due_date_obj = None
                if due_date:
                    try:
                        due_date_obj = datetime.strptime(due_date, '%Y-%m-%d')
                    except ValueError:
                        pass

                payment_method_type = f"stripe_{payment_method}"
                billing_data = BillingCreate(
                    invoice_number=invoice_number,
                    user_id=current_user.id,
                    job_id=job_id,
                    customer_name=client_name,
                    invoice_amount=contract_amount,
                    invoice_date=datetime.now(),
                    due_date=due_date_obj,
                    paid=False,
                    stripe_payment_intent_id=stripe_payment_intent_id,
                    stripe_checkout_session_id=stripe_result.get("checkout_session_id"),
                    payment_method_type=payment_method_type
                )
                billing_record = create_billing(
                    db=db,
                    billing=billing_data,
                    customer_email=customer_email,
                    payment_url=stripe_checkout_url
                )
                print(f"Created new billing with Stripe {payment_method}: {invoice_number}")
        else:
            # Regular invoice (no Stripe)
            existing_unpaid_billing = get_unpaid_billing_by_job_id(db=db, job_id=job_id)

            if existing_unpaid_billing:
                # Use existing unpaid billing record
                billing_record = existing_unpaid_billing
                invoice_number = billing_record.invoice_number
                print(f"Using existing unpaid billing: {invoice_number}")
            else:
                # Create new billing record
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                invoice_number = f"INV-{job_id}-{timestamp}"

                # Parse due date for billing record
                due_date_obj = None
                if due_date:
                    try:
                        due_date_obj = datetime.strptime(due_date, '%Y-%m-%d')
                    except ValueError:
                        pass

                billing_data = BillingCreate(
                    invoice_number=invoice_number,
                    user_id=current_user.id,
                    job_id=job_id,
                    customer_name=client_name,
                    invoice_amount=contract_amount,
                    invoice_date=datetime.now(),
                    due_date=due_date_obj,
                    paid=False
                )
                billing_record = create_billing(
                    db=db,
                    billing=billing_data,
                    customer_email=customer_email
                )
                print(f"Created new regular billing: {invoice_number}")

        # Generate the invoice HTML in memory with optional Stripe link
        html_bytes = create_job_invoice_in_memory(
            job_name=job_name,
            client_name=client_name,
            invoice_number=invoice_number,
            project_deadline=due_date,
            contract_amount=contract_amount,
            stripe_checkout_url=stripe_checkout_url
        )

        # Create filename for download
        filename = f"invoice_{job_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"

        # If Stripe payment was created, add Stripe info to response headers
        headers = {"Content-Disposition": f"attachment; filename={filename}"}
        if create_stripe_payment and stripe_checkout_url:
            if stripe_payment_intent_id:
                headers["X-Stripe-Payment-Intent-ID"] = str(stripe_payment_intent_id)
            headers["X-Stripe-Checkout-URL"] = str(stripe_checkout_url)
            headers["X-Customer-Email"] = str(customer_email)

        # Return HTML as download (can be opened in browser or converted to PDF)
        from fastapi.responses import Response
        return Response(
            content=html_bytes,
            media_type="text/html",
            headers=headers
        )

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to generate invoice: {str(e)}"
        )








@router.post("/{job_id}/create-stripe-ach-invoice", summary="Create Stripe ACH invoice for job")
async def create_stripe_ach_invoice_for_job(
    job_id: int,
    customer_email: str = Form(..., description="Customer email address for Stripe invoice"),
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user)
):
    """
    Create a Stripe ACH (e-check) payment for a specific job

    **Parameters:**
    - `job_id`: ID of the job to create Stripe ACH payment for
    - `customer_email`: Customer's email address for Stripe payment

    **Returns:**
    - JSON response with Stripe payment details and checkout URL

    **Stripe ACH Integration:**
    - Creates Stripe Checkout Session for ACH payments
    - Provides hosted payment page for bank account collection
    - Handles ACH payment processing (3-5 business days)
    - Updates billing record with Stripe payment intent ID
    - Generates invoice with Stripe payment link

    **ACH Payment Features:**
    - Bank account verification (automatic or instant)
    - Lower processing fees compared to credit cards
    - Suitable for larger invoice amounts
    - Automatic payment status updates via webhooks
    """
    try:
        # Get the job
        job = get_job_by_id(db=db, job_id=job_id)
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")

        # Check if user can access this job
        if current_user.role.value != "admin" and job.client_name != current_user.full_name:
            raise HTTPException(
                status_code=403,
                detail="Not authorized to create invoice for this job"
            )

        # Validate required job fields
        if not job.contract_amount:
            raise HTTPException(
                status_code=400,
                detail="Job must have a contract amount to create an invoice"
            )

        if not job.client_name:
            raise HTTPException(
                status_code=400,
                detail="Job must have a client name to create an invoice"
            )

        # Parse contract amount
        contract_amount = 0.0
        try:
            amount_str = str(job.contract_amount).replace('$', '').replace(',', '').strip()
            contract_amount = float(amount_str)
        except (ValueError, TypeError):
            raise HTTPException(
                status_code=400,
                detail="Invalid contract amount format"
            )

        # Check if a Stripe payment already exists for this job
        existing_billing = get_unpaid_billing_by_job_id(db=db, job_id=job_id)

        if existing_billing and existing_billing.stripe_payment_intent_id:
            # Use existing Stripe payment
            print(f"Using existing Stripe payment for job {job_id}: {existing_billing.stripe_payment_intent_id}")

            # Get Stripe payment status
            payment_status = stripe_service.get_payment_intent_status(existing_billing.stripe_payment_intent_id)
            if payment_status["success"]:
                return {
                    "success": True,
                    "message": f"Using existing Stripe ACH payment (Status: {payment_status['status']})",
                    "stripe_payment_intent_id": existing_billing.stripe_payment_intent_id,
                    "invoice_number": existing_billing.invoice_number,
                    "billing_id": existing_billing.id,
                    "customer_email": customer_email,
                    "amount": float(existing_billing.invoice_amount),
                    "due_date": existing_billing.due_date.isoformat() if existing_billing.due_date else None,
                    "existing_payment": True,
                    "payment_status": payment_status["status"]
                }

        # Generate invoice number
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        invoice_number = f"INV-{job_id}-{timestamp}"

        # Prepare job details
        job_name = job.job_name or f"Job #{job_id}"
        client_name = job.client_name

        # Set success and cancel URLs
        base_url = settings.BASE_URL
        success_url = f"{base_url}/api/v1/public/payment-success?session_id={{CHECKOUT_SESSION_ID}}"
        cancel_url = f"{base_url}/api/v1/jobs/payment-cancelled"

        # Create Stripe Checkout Session for ACH
        stripe_result = stripe_service.create_checkout_session_for_ach(
            amount=contract_amount,
            customer_email=customer_email,
            customer_name=client_name,
            invoice_number=invoice_number,
            job_name=job_name,
            success_url=success_url,
            cancel_url=cancel_url
        )

        if not stripe_result["success"]:
            raise HTTPException(
                status_code=500,
                detail=f"Failed to create Stripe ACH payment: {stripe_result.get('message', 'Unknown error')}"
            )

        # Set due date (project deadline or 30 days from now)
        if job.project_deadline:
            due_date = datetime.combine(job.project_deadline, datetime.min.time())
        else:
            due_date = datetime.now() + timedelta(days=30)

        # Update or create billing record
        if existing_billing:
            # Update existing billing record with Stripe payment intent ID
            billing_update = BillingUpdate(
                invoice_number=invoice_number,
                stripe_payment_intent_id=stripe_result["payment_intent_id"],
                payment_method_type="stripe_ach"
            )
            billing_record = update_billing(db=db, billing_id=existing_billing.id, billing_update=billing_update)
        else:
            # Create new billing record
            billing_data = BillingCreate(
                invoice_number=invoice_number,
                user_id=current_user.id,
                job_id=job_id,
                customer_name=client_name,
                invoice_amount=contract_amount,
                invoice_date=datetime.now(),
                due_date=due_date,
                paid=False,
                stripe_payment_intent_id=stripe_result["payment_intent_id"],
                payment_method_type="stripe_ach"
            )
            billing_record = create_billing(
                db=db,
                billing=billing_data,
                customer_email=customer_email,
                payment_url=stripe_result["checkout_url"]
            )

        # Return success response with Stripe details
        return {
            "success": True,
            "message": "Stripe ACH payment created successfully",
            "stripe_checkout_session_id": stripe_result["checkout_session_id"],
            "stripe_checkout_url": stripe_result["checkout_url"],
            "stripe_payment_intent_id": stripe_result["payment_intent_id"],
            "invoice_number": invoice_number,
            "billing_id": billing_record.id,
            "customer_email": customer_email,
            "amount": float(contract_amount),
            "due_date": due_date.isoformat(),
            "payment_method": "stripe_ach"
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create Stripe ACH payment: {str(e)}"
        )


@router.post("/{job_id}/create-stripe-card-payment", summary="Create Stripe card payment for job")
async def create_stripe_card_payment_for_job(
    job_id: int,
    customer_email: str = Form(..., description="Customer email address for Stripe payment"),
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user)
):
    """
    Create a Stripe checkout session for credit/debit card payments for a specific job

    **Parameters:**
    - `job_id`: ID of the job to create Stripe card payment for
    - `customer_email`: Customer's email address for Stripe payment

    **Returns:**
    - JSON response with Stripe checkout session details and payment URL

    **Stripe Card Integration:**
    - Creates checkout session in Stripe for credit/debit card payments
    - Provides hosted payment page for secure card collection
    - Instant payment processing (no waiting period like ACH)
    - Higher processing fees compared to ACH
    - Updates billing record with Stripe payment intent ID
    - Tracks payment status through Stripe webhooks

    **Billing Integration:**
    - Saves billing information to database
    - Links Stripe payment intent ID to billing record
    - Tracks payment status through Stripe webhooks
    - Automatically updates job status when payment is received
    """
    try:
        # Get the job
        job = get_job_by_id(db=db, job_id=job_id)
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")

        # Check if user can access this job
        if current_user.role.value != "admin" and job.client_name != current_user.full_name:
            raise HTTPException(
                status_code=403,
                detail="Not authorized to create payment for this job"
            )

        # Validate required job fields
        if not job.contract_amount:
            raise HTTPException(
                status_code=400,
                detail="Job must have a contract amount to create a payment"
            )

        if not job.client_name:
            raise HTTPException(
                status_code=400,
                detail="Job must have a client name to create a payment"
            )

        # Check if a Stripe payment already exists for this job
        existing_billing = get_unpaid_billing_by_job_id(db=db, job_id=job_id)

        if existing_billing and existing_billing.stripe_payment_intent_id:
            raise HTTPException(
                status_code=400,
                detail=f"A Stripe payment already exists for this job. Billing ID: {existing_billing.id}"
            )

        # Create new Stripe card payment
        print(f"Creating new Stripe card payment for job {job_id}")

        # Generate invoice number
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        invoice_number = f"INV-{job_id}-{timestamp}"

        # Prepare job details
        job_name = job.job_name or f"Job #{job_id}"
        client_name = job.client_name
        contract_amount = job.contract_amount

        # Set success and cancel URLs
        base_url = settings.BASE_URL
        success_url = f"{base_url}/api/v1/public/payment-success?session_id={{CHECKOUT_SESSION_ID}}"
        cancel_url = f"{base_url}/api/v1/jobs/payment-cancelled"

        # Create Stripe Checkout Session for Cards
        stripe_result = stripe_service.create_checkout_session_for_cards(
            amount=contract_amount,
            customer_email=customer_email,
            customer_name=client_name,
            invoice_number=invoice_number,
            job_name=job_name,
            success_url=success_url,
            cancel_url=cancel_url
        )

        if not stripe_result["success"]:
            raise HTTPException(
                status_code=500,
                detail=f"Failed to create Stripe card payment: {stripe_result.get('message', 'Unknown error')}"
            )

        # Set due date (project deadline or 30 days from now)
        if job.project_deadline:
            due_date = datetime.combine(job.project_deadline, datetime.min.time())
        else:
            due_date = datetime.now() + timedelta(days=30)

        # Update or create billing record
        if existing_billing:
            # Update existing billing record with Stripe payment intent ID
            billing_update = BillingUpdate(
                invoice_number=invoice_number,
                stripe_payment_intent_id=stripe_result["payment_intent_id"],
                payment_method_type="stripe_card"
            )
            billing_record = update_billing(db=db, billing_id=existing_billing.id, billing_update=billing_update)
        else:
            # Create new billing record
            billing_data = BillingCreate(
                invoice_number=invoice_number,
                user_id=current_user.id,
                job_id=job_id,
                customer_name=client_name,
                invoice_amount=contract_amount,
                invoice_date=datetime.now(),
                due_date=due_date,
                paid=False,
                stripe_payment_intent_id=stripe_result["payment_intent_id"],
                payment_method_type="stripe_card"
            )
            billing_record = create_billing(
                db=db,
                billing=billing_data,
                customer_email=customer_email,
                payment_url=stripe_result["checkout_url"]
            )

        # Return success response with Stripe details
        return {
            "success": True,
            "message": "Stripe card payment created successfully",
            "stripe_checkout_session_id": stripe_result["checkout_session_id"],
            "stripe_checkout_url": stripe_result["checkout_url"],
            "stripe_payment_intent_id": stripe_result["payment_intent_id"],
            "invoice_number": invoice_number,
            "billing_id": billing_record.id,
            "customer_email": customer_email,
            "amount": float(contract_amount),
            "due_date": due_date.isoformat(),
            "payment_method": "stripe_card"
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create Stripe card payment: {str(e)}"
        )


@router.get("/{job_id}/stripe-payment-status", summary="Get Stripe payment status for job")
async def get_stripe_payment_status(
    job_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user)
):
    """
    Get Stripe payment status for a specific job

    **Parameters:**
    - `job_id`: ID of the job to check Stripe payment status for

    **Returns:**
    - JSON response with Stripe payment status and details
    """
    try:
        # Get the job
        job = get_job_by_id(db=db, job_id=job_id)
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")

        # Check if user can access this job
        if current_user.role.value != "admin" and job.client_name != current_user.full_name:
            raise HTTPException(
                status_code=403,
                detail="Not authorized to access this job"
            )

        # Get billing record with Stripe payment intent ID
        billing_record = get_unpaid_billing_by_job_id(db=db, job_id=job_id)
        if not billing_record or not billing_record.stripe_payment_intent_id:
            raise HTTPException(
                status_code=404,
                detail="No Stripe payment found for this job"
            )

        # Get Stripe payment intent details
        stripe_result = stripe_service.get_payment_intent_status(billing_record.stripe_payment_intent_id)

        if not stripe_result["success"]:
            raise HTTPException(
                status_code=500,
                detail=f"Failed to get Stripe payment status: {stripe_result.get('message', 'Unknown error')}"
            )

        return {
            "success": True,
            "stripe_payment_intent_id": billing_record.stripe_payment_intent_id,
            "stripe_status": stripe_result["status"],
            "billing_id": billing_record.id,
            "invoice_number": billing_record.invoice_number,
            "local_paid_status": billing_record.paid,
            "amount": stripe_result["amount"],
            "currency": stripe_result["currency"],
            "is_paid": stripe_result["is_paid"],
            "payment_method_type": billing_record.payment_method_type,
            "metadata": stripe_result["metadata"]
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get Stripe payment status: {str(e)}"
        )


@router.get("/{job_id}/generate-invoice-with-stripe", summary="Generate invoice with Stripe ACH payment option")
async def generate_job_invoice_with_stripe(
    job_id: int,
    customer_email: str = Query(..., description="Customer email for Stripe payment"),
    create_stripe_payment: bool = Query(True, description="Create Stripe ACH payment and include payment link"),
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user)
):
    """
    Generate and download a PDF invoice for a specific job with Stripe ACH payment integration

    **Parameters:**
    - `job_id`: ID of the job to generate invoice for
    - `customer_email`: Customer email for Stripe payment (required if create_stripe_payment=true)
    - `create_stripe_payment`: Whether to create Stripe ACH payment and include payment link in PDF

    **Returns:**
    - HTML file download (application/html)

    **Invoice Details:**
    - Company: DOTec Corp. information
    - Client: Job client name
    - Project: Job name/description
    - Amount: Contract amount from job (no tax added)
    - Due Date: Project deadline or 30 days from invoice date
    - Invoice Number: Auto-generated based on job ID and timestamp
    - Payment Link: Stripe ACH payment link (if Stripe payment created)

    **Stripe ACH Integration (when create_stripe_payment=true):**
    - Creates Stripe Checkout Session for ACH payments
    - Provides hosted payment page for bank account collection
    - Embeds Stripe payment link in PDF
    - Stores Stripe payment intent ID in billing record
    - Enables automatic payment status updates via webhooks

    **Features:**
    - Generated on-the-fly (not saved to disk)
    - Professional HTML format with integrated Stripe ACH payments
    - Auto-generated invoice numbers
    - Clickable payment links for ACH bank transfers
    - Smart billing: Reuses unpaid invoices instead of creating duplicates
    - Lower processing fees compared to credit cards
    """

    # Get the job
    job = get_job_by_id(db=db, job_id=job_id)
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")

    # Check if user can access this job
    if current_user.role.value != "admin" and job.client_name != current_user.full_name:
        raise HTTPException(
            status_code=403,
            detail="Not authorized to generate invoice for this job"
        )

    # Prepare invoice data
    client_name = job.client_name or "Client"
    job_name = job.job_name
    project_deadline = job.project_deadline.strftime('%Y-%m-%d') if job.project_deadline else None

    # If no deadline, set due date to 30 days from now
    if not project_deadline:
        from datetime import timedelta
        due_date = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')
    else:
        due_date = project_deadline

    # Parse contract amount
    contract_amount = 0.0
    if job.contract_amount:
        try:
            # Remove currency symbols and commas, then convert to float
            amount_str = str(job.contract_amount).replace('$', '').replace(',', '').strip()
            contract_amount = float(amount_str)
        except (ValueError, TypeError):
            contract_amount = 0.0

    try:
        # Stripe checkout URL for PDF integration
        stripe_checkout_url = None
        stripe_payment_intent_id = None

        # Check if Stripe payment should be created
        if create_stripe_payment:
            if not customer_email:
                raise HTTPException(
                    status_code=400,
                    detail="Customer email is required when creating Stripe payment"
                )

            # First, check if a Stripe payment already exists for this job
            existing_billing = get_unpaid_billing_by_job_id(db=db, job_id=job_id)

            if existing_billing and existing_billing.stripe_payment_intent_id:
                # Use existing Stripe payment
                print(f"Using existing Stripe payment: {existing_billing.stripe_payment_intent_id}")
                stripe_payment_intent_id = existing_billing.stripe_payment_intent_id

                # For existing payments, we need to construct the checkout URL
                # In a real implementation, you'd store the checkout session ID or URL
                stripe_checkout_url = f"https://checkout.stripe.com/pay/cs_test_existing#{stripe_payment_intent_id}"
                print(f"Using existing Stripe checkout URL: {stripe_checkout_url}")
            else:
                # Create new Stripe checkout session
                print("Creating new Stripe checkout session...")

                # Generate invoice number for Stripe
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                invoice_number = f"INV-{job_id}-{timestamp}"

                # Set success and cancel URLs
                base_url = settings.BASE_URL
                success_url = f"{base_url}/api/v1/public/payment-success?session_id={{CHECKOUT_SESSION_ID}}"
                cancel_url = f"{base_url}/api/v1/jobs/payment-cancelled"

                # Create Stripe checkout session
                stripe_result = stripe_service.create_checkout_session_for_ach(
                    amount=contract_amount,
                    customer_email=customer_email,
                    customer_name=client_name,
                    invoice_number=invoice_number,
                    job_name=job_name,
                    success_url=success_url,
                    cancel_url=cancel_url
                )

                if not stripe_result["success"]:
                    raise HTTPException(
                        status_code=500,
                        detail=f"Failed to create Stripe checkout session: {stripe_result.get('message', 'Unknown error')}"
                    )

                stripe_checkout_url = stripe_result["checkout_url"]
                stripe_payment_intent_id = stripe_result["payment_intent_id"]
                print(f"Created new Stripe checkout session: {stripe_result['checkout_session_id']}")

        # Handle billing record creation/update
        if create_stripe_payment:
            # When creating Stripe payment, we already checked for existing billing above
            existing_unpaid_billing = get_unpaid_billing_by_job_id(db=db, job_id=job_id)

            if existing_unpaid_billing and existing_unpaid_billing.stripe_payment_intent_id:
                # Use existing billing record with Stripe payment
                billing_record = existing_unpaid_billing
                invoice_number = billing_record.invoice_number
                print(f"Using existing billing with Stripe: {invoice_number}")
            elif existing_unpaid_billing:
                # Update existing billing record with new Stripe info
                billing_update = BillingUpdate(
                    stripe_payment_intent_id=stripe_payment_intent_id,
                    payment_method_type="stripe_ach"
                )
                billing_record = update_billing(db=db, billing_id=existing_unpaid_billing.id, billing_update=billing_update)
                invoice_number = billing_record.invoice_number
                print(f"Updated existing billing with Stripe: {invoice_number}")
            else:
                # Create new billing record with Stripe info
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                invoice_number = f"INV-{job_id}-{timestamp}"

                # Parse due date for billing record
                due_date_obj = None
                if due_date:
                    try:
                        due_date_obj = datetime.strptime(due_date, '%Y-%m-%d')
                    except ValueError:
                        pass

                billing_data = BillingCreate(
                    invoice_number=invoice_number,
                    user_id=current_user.id,
                    job_id=job_id,
                    customer_name=client_name,
                    invoice_amount=contract_amount,
                    invoice_date=datetime.now(),
                    due_date=due_date_obj,
                    paid=False,
                    stripe_payment_intent_id=stripe_payment_intent_id,
                    payment_method_type="stripe_ach"
                )
                billing_record = create_billing(
                    db=db,
                    billing=billing_data,
                    customer_email=customer_email,
                    payment_url=stripe_checkout_url
                )
                print(f"Created new billing with Stripe: {invoice_number}")
        else:
            # Regular invoice (no Stripe)
            existing_unpaid_billing = get_unpaid_billing_by_job_id(db=db, job_id=job_id)

            if existing_unpaid_billing:
                # Use existing unpaid billing record
                billing_record = existing_unpaid_billing
                invoice_number = billing_record.invoice_number
                print(f"Using existing unpaid billing: {invoice_number}")
            else:
                # Create new billing record
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                invoice_number = f"INV-{job_id}-{timestamp}"

                # Parse due date for billing record
                due_date_obj = None
                if due_date:
                    try:
                        due_date_obj = datetime.strptime(due_date, '%Y-%m-%d')
                    except ValueError:
                        pass

                billing_data = BillingCreate(
                    invoice_number=invoice_number,
                    user_id=current_user.id,
                    job_id=job_id,
                    customer_name=client_name,
                    invoice_amount=contract_amount,
                    invoice_date=datetime.now(),
                    due_date=due_date_obj,
                    paid=False
                )
                billing_record = create_billing(
                    db=db,
                    billing=billing_data,
                    customer_email=customer_email
                )
                print(f"Created new regular billing: {invoice_number}")

        # Generate the invoice HTML in memory with optional Stripe link
        html_bytes = create_job_invoice_in_memory(
            job_name=job_name,
            client_name=client_name,
            invoice_number=invoice_number,
            project_deadline=due_date,
            contract_amount=contract_amount,
            stripe_checkout_url=stripe_checkout_url
        )

        # Create filename for download
        filename = f"invoice_{job_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"

        # If Stripe payment was created, add Stripe info to response headers
        headers = {"Content-Disposition": f"attachment; filename={filename}"}
        if create_stripe_payment and stripe_checkout_url:
            if stripe_payment_intent_id:
                headers["X-Stripe-Payment-Intent-ID"] = str(stripe_payment_intent_id)
            headers["X-Stripe-Checkout-URL"] = str(stripe_checkout_url)
            headers["X-Customer-Email"] = str(customer_email)

        # Return HTML as download (can be opened in browser or converted to PDF)
        from fastapi.responses import Response
        return Response(
            content=html_bytes,
            media_type="text/html",
            headers=headers
        )

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to generate invoice: {str(e)}"
        )


@router.post("/{job_id}/check-payment-status", summary="Manually check and update payment status")
async def check_and_update_payment_status(
    job_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user)
):
    """
    Manually check Stripe payment status and update job/billing records

    **Parameters:**
    - `job_id`: ID of the job to check payment status for

    **Purpose:**
    - Manually trigger payment status check if webhooks aren't working
    - Debug payment processing issues
    - Force update job status when payment is detected

    **Returns:**
    - Payment status details and any updates made
    """
    try:
        # Get the job
        job = get_job_by_id(db=db, job_id=job_id)
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")

        # Check if user can access this job
        if current_user.role.value != "admin" and job.client_name != current_user.full_name:
            raise HTTPException(
                status_code=403,
                detail="Not authorized to check payment status for this job"
            )

        # Get billing records for this job
        from app.crud.billing import get_billings_by_job_id
        billing_records = db.query(Billing).filter(Billing.job_id == job_id).all()

        if not billing_records:
            return {
                "success": False,
                "message": "No billing records found for this job",
                "job_id": job_id,
                "job_status": job.job_status.value
            }

        results = {
            "job_id": job_id,
            "job_name": job.job_name,
            "client_name": job.client_name,
            "current_job_status": job.job_status.value,
            "billing_records": [],
            "updates_made": [],
            "payment_status_summary": {
                "total_billings": len(billing_records),
                "paid_billings": 0,
                "unpaid_billings": 0,
                "stripe_billings": 0
            }
        }

        job_updated = False

        for billing in billing_records:
            billing_info = {
                "billing_id": billing.id,
                "invoice_number": billing.invoice_number,
                "amount": float(billing.invoice_amount),
                "currently_paid": billing.paid,
                "stripe_payment_intent_id": billing.stripe_payment_intent_id,
                "payment_method_type": billing.payment_method_type
            }

            # Update counters
            if billing.paid:
                results["payment_status_summary"]["paid_billings"] += 1
            else:
                results["payment_status_summary"]["unpaid_billings"] += 1

            if billing.stripe_payment_intent_id:
                results["payment_status_summary"]["stripe_billings"] += 1

            # Check Stripe payment status if we have a payment intent ID
            if billing.stripe_payment_intent_id and not billing.paid:
                print(f"🔍 Checking Stripe payment status for billing {billing.id}")

                # Get payment status from Stripe
                payment_status = stripe_service.get_payment_intent_status(billing.stripe_payment_intent_id)

                billing_info["stripe_status_check"] = payment_status

                if payment_status["success"] and payment_status["is_paid"]:
                    print(f"✅ Payment detected for billing {billing.id} - updating records")

                    # Mark billing as paid
                    from app.crud.billing import mark_billing_as_paid
                    updated_billing = mark_billing_as_paid(db, billing.id)

                    if updated_billing:
                        billing_info["updated_to_paid"] = True
                        results["updates_made"].append(f"Marked billing {billing.id} as paid")
                        results["payment_status_summary"]["paid_billings"] += 1
                        results["payment_status_summary"]["unpaid_billings"] -= 1

                        # Check if we should update job status
                        if job.job_status != JobStatus.COMPLETED:
                            job_updated = True
                    else:
                        billing_info["update_failed"] = True
                        results["updates_made"].append(f"Failed to update billing {billing.id}")
                else:
                    billing_info["stripe_payment_pending"] = True
                    print(f"⏳ Payment still pending for billing {billing.id}")

            results["billing_records"].append(billing_info)

        # Update job status if payments were found and job isn't completed
        if job_updated and job.job_status != JobStatus.COMPLETED:
            # Check if all Stripe billings are now paid
            stripe_billings = [b for b in billing_records if b.stripe_payment_intent_id]
            if stripe_billings:
                all_stripe_paid = all(b.paid for b in stripe_billings)
                if all_stripe_paid:
                    print(f"🎉 All Stripe payments completed - updating job {job_id} to COMPLETED")
                    updated_job = update_job_status_on_payment(db, job_id, JobStatus.COMPLETED)
                    if updated_job:
                        results["job_status_updated"] = True
                        results["new_job_status"] = "COMPLETED"
                        results["updates_made"].append(f"Updated job {job_id} status to COMPLETED")
                    else:
                        results["job_update_failed"] = True
                        results["updates_made"].append(f"Failed to update job {job_id} status")

        return {
            "success": True,
            "message": "Payment status check completed",
            "results": results
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to check payment status: {str(e)}"
        )


@router.post("/check-all-pending-payments", summary="Check all pending Stripe payments")
async def check_all_pending_payments(
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user)
):
    """
    Check all pending Stripe payments and update billing/job status automatically

    **Purpose:**
    - Check all unpaid billing records with Stripe session/payment intent IDs
    - Update billing status to paid if payment is completed
    - Update job status to completed if all payments are received
    - Can be called periodically or on each request for real-time updates

    **Returns:**
    - Summary of payment status updates
    - List of newly paid invoices
    - List of completed jobs

    **Usage:**
    - Call this endpoint periodically (e.g., every few minutes)
    - Call on page load to ensure up-to-date payment status
    - Use instead of webhooks for reliable payment detection
    """
    try:
        logger.info("🔍 Manual check of all pending Stripe payments initiated")

        # Use the Stripe service to check and update pending payments
        result = stripe_service.check_and_update_pending_payments(db)

        if result["success"]:
            results = result["results"]

            return {
                "success": True,
                "message": f"Payment check completed. {results['newly_paid']} payments updated, {len(results['completed_jobs'])} jobs completed.",
                "summary": {
                    "total_checked": results["total_checked"],
                    "newly_paid": results["newly_paid"],
                    "still_pending": results["still_pending"],
                    "errors": results["errors"],
                    "jobs_completed": len(results["completed_jobs"])
                },
                "updated_payments": results["updated_payments"],
                "completed_jobs": results["completed_jobs"],
                "error_details": results["error_details"] if results["errors"] > 0 else None
            }
        else:
            return {
                "success": False,
                "message": f"Failed to check pending payments: {result.get('message', 'Unknown error')}",
                "error": result.get("error")
            }

    except Exception as e:
        logger.error(f"❌ Error in check_all_pending_payments: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to check pending payments: {str(e)}"
        )




@router.get("/payment-cancelled", summary="Payment cancelled page")
async def payment_cancelled():
    """
    Payment cancelled page - shows when user cancels payment

    **Returns:**
    - HTML page showing payment cancellation
    """
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Payment Cancelled - DOTec Hub</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 40px;
                background-color: #f5f5f5;
                line-height: 1.6;
            }
            .container {
                max-width: 600px;
                margin: 0 auto;
                background: white;
                padding: 40px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .cancelled {
                color: #dc3545;
                text-align: center;
            }
            .icon {
                font-size: 48px;
                text-align: center;
                margin-bottom: 20px;
            }
            .actions {
                text-align: center;
                margin-top: 30px;
            }
            .btn {
                display: inline-block;
                padding: 10px 20px;
                background-color: #007bff;
                color: white;
                text-decoration: none;
                border-radius: 4px;
                margin: 0 10px;
            }
            .btn:hover {
                background-color: #0056b3;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="icon">❌</div>
            <h1 class="cancelled">Payment Cancelled</h1>
            <p style="text-align: center; font-size: 18px;">
                Your payment was cancelled. No charges have been made to your account.
            </p>
            <p style="text-align: center;">
                You can try again or contact us if you need assistance.
            </p>

            <div class="actions">
                <a href="{settings.BASE_URL}" class="btn">Return to Dashboard</a>
            </div>
        </div>
    </body>
    </html>
    """

    from fastapi.responses import HTMLResponse
    return HTMLResponse(content=html_content)


@router.get("/debug/config", summary="Debug configuration")
async def debug_config():
    """
    Debug endpoint to check current configuration

    **Returns:**
    - Current BASE_URL and other settings
    """
    return {
        "BASE_URL": settings.BASE_URL,
        "STRIPE_MODE": settings.STRIPE_MODE,
        "success_url_template": f"{settings.BASE_URL}/api/v1/public/payment-success?session_id={{CHECKOUT_SESSION_ID}}",
        "cancel_url_template": f"{settings.BASE_URL}/api/v1/jobs/payment-cancelled",
        "message": "These are the URLs that will be sent to Stripe"
    }


def process_paid_jobs_cash_receipts(db: Session) -> dict:
    """
    Check for paid jobs and create cash receipts in Deltek
    """
    try:
        # Find paid billings for Deltek jobs that don't have cash receipts yet
        paid_billings_needing_receipts = db.query(Billing).join(JobModel).filter(
            Billing.paid == True,
            Billing.is_cash_receipt_generated == False,
            JobModel.is_deltek_import == True,
            JobModel.deltek_invoice_id.isnot(None),
            JobModel.deltek_project_number.isnot(None)
        ).all()

        if not paid_billings_needing_receipts:
            logger.info("✅ No paid jobs requiring cash receipts found")
            return {
                "total_processed": 0,
                "successful": 0,
                "failed": 0,
                "errors": []
            }

        logger.info(f"💰 Found {len(paid_billings_needing_receipts)} paid jobs needing cash receipts")

        cash_receipt_stats = {
            "total_processed": len(paid_billings_needing_receipts),
            "successful": 0,
            "failed": 0,
            "errors": []
        }

        for billing in paid_billings_needing_receipts:
            try:
                job = billing.job
                logger.info(f"💰 Creating cash receipt for billing {billing.id} (job {job.id}: {job.job_name})")

                # Prepare billing data for cash receipt
                billing_data = {
                    "job_id": job.id,
                    "job_name": job.job_name,
                    "deltek_project_number": job.deltek_project_number,
                    "deltek_invoice_id": job.deltek_invoice_id,
                    "amount": float(billing.invoice_amount)
                }

                # Create cash receipt in Deltek
                cash_receipt_result = deltek_service.create_cash_receipt(billing_data)

                if cash_receipt_result["success"]:
                    # Mark cash receipt as generated
                    billing.is_cash_receipt_generated = True
                    db.commit()

                    cash_receipt_stats["successful"] += 1
                    logger.info(f"✅ Cash receipt created successfully for billing {billing.id}")
                else:
                    cash_receipt_stats["failed"] += 1
                    error_msg = f"Failed to create cash receipt for billing {billing.id}: {cash_receipt_result.get('message', 'Unknown error')}"
                    logger.error(f"❌ {error_msg}")
                    cash_receipt_stats["errors"].append(error_msg)

            except Exception as e:
                cash_receipt_stats["failed"] += 1
                error_msg = f"Error processing cash receipt for billing {billing.id}: {str(e)}"
                logger.error(f"❌ {error_msg}")
                cash_receipt_stats["errors"].append(error_msg)
                db.rollback()
                continue

        # Log summary
        logger.info("💰 Cash Receipt Processing Summary:")
        logger.info(f"   Total processed: {cash_receipt_stats['total_processed']}")
        logger.info(f"   Successful: {cash_receipt_stats['successful']}")
        logger.info(f"   Failed: {cash_receipt_stats['failed']}")

        return cash_receipt_stats

    except Exception as e:
        logger.error(f"❌ Unexpected error during cash receipt processing: {str(e)}")
        return {
            "total_processed": 0,
            "successful": 0,
            "failed": 0,
            "errors": [str(e)]
        }


@router.post("/deltek-sync", summary="Manually trigger Deltek invoice synchronization")
async def manual_deltek_sync(
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_admin)
):
    """
    Manually trigger Deltek invoice synchronization (Admin only)

    **Purpose:**
    - Fetch invoices from Deltek API
    - Import new invoices as jobs/projects
    - Skip existing invoices to avoid duplicates

    **Returns:**
    - Summary of synchronization results
    - List of newly created jobs
    - Statistics about the sync process

    **Usage:**
    - Use this endpoint to manually sync Deltek data
    - Useful for testing or immediate synchronization
    - The same process runs automatically every hour
    """
    try:
        logger.info("🔄 Manual Deltek synchronization triggered by admin")

        # Fetch invoices from Deltek API
        logger.info("📡 Fetching invoices from Deltek API...")
        fetch_result = deltek_service.get_ar_balance_invoices()

        if not fetch_result["success"]:
            logger.error(f"❌ Failed to fetch invoices: {fetch_result['message']}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to fetch invoices from Deltek: {fetch_result['message']}"
            )

        invoices = fetch_result["invoices"]
        logger.info(f"📋 Fetched {len(invoices)} invoices from Deltek")

        # Process each invoice
        stats = {
            "total_invoices": len(invoices),
            "new_jobs_created": 0,
            "skipped_duplicates": 0,
            "errors": 0,
            "created_jobs": [],
            "error_details": []
        }

        for invoice in invoices:
            try:
                # Parse invoice data
                parse_result = deltek_service.parse_invoice_data(invoice)


                if not parse_result["success"]:
                    error_msg = f"Failed to parse invoice: {parse_result['message']}"
                    logger.error(f"❌ {error_msg}")
                    stats["errors"] += 1
                    stats["error_details"].append(error_msg)
                    continue

                parsed_data = parse_result["parsed_data"]
                invoice_id = parsed_data["invoice_id"]
                if(parsed_data['project_number'] == "B2411223_000"):
                        project_details = deltek_service.project_details(parsed_data['project_number'])
                        client_email = project_details['project']['BillingClientEmail']
                # Check if invoice already exists
                existing_job = db.query(JobModel).filter(JobModel.deltek_invoice_id == invoice_id).first()
                if existing_job:
                    logger.debug(f"⏭️ Skipping duplicate invoice {invoice_id}")
                    stats["skipped_duplicates"] += 1
                    
                        # job_info['client_email']
                    continue

                # Create job from invoice
                job_data = JobCreate(
                    job_name=parsed_data["project_name"] or f"Project {parsed_data['project_number']}",
                    job_description=f"Invoice: {invoice_id}",
                    client_name=parsed_data["client_name"],
                    contract_amount=str(parsed_data["amount"]),
                    job_status=JobStatus.PENDING_PAYMENT,
                    project_deadline=parsed_data["due_date"].date() if parsed_data["due_date"] else None
                )

                # Create the job
                new_job = create_job(db=db, job=job_data)

                # Update with Deltek-specific fields
                new_job.deltek_invoice_id = invoice_id
                new_job.deltek_project_number = parsed_data["project_number"]
                new_job.deltek_billing_client_id = parsed_data["billing_client_id"]
                new_job.deltek_original_amount = str(parsed_data["original_amount"])
                new_job.deltek_invoice_date = parsed_data["invoice_date"]
                new_job.is_deltek_import = True
                new_job.deltek_last_sync = datetime.now()
                job_info = {
                    "job_id": new_job.id,
                    "job_name": new_job.job_name,
                    "client_name": new_job.client_name,
                    "invoice_id": invoice_id,
                    "amount": parsed_data["amount"]
                }
                
                db.commit()
                db.refresh(new_job)

                job_info = {
                    "job_id": new_job.id,
                    "job_name": new_job.job_name,
                    "client_name": new_job.client_name,
                    "invoice_id": invoice_id,
                    "amount": parsed_data["amount"]
                }

                logger.info(f"✅ Created job {new_job.id} for invoice {invoice_id} - {parsed_data['client_name']}")
                stats["new_jobs_created"] += 1
                stats["created_jobs"].append(job_info)

            except Exception as e:
                error_msg = f"Error processing invoice: {str(e)}"
                logger.error(f"❌ {error_msg}")
                stats["errors"] += 1
                stats["error_details"].append(error_msg)
                db.rollback()
                continue

        # Log summary
        logger.info("📊 Manual Deltek Sync Summary:")
        logger.info(f"   Total invoices: {stats['total_invoices']}")
        logger.info(f"   New jobs created: {stats['new_jobs_created']}")
        logger.info(f"   Skipped duplicates: {stats['skipped_duplicates']}")
        logger.info(f"   Errors: {stats['errors']}")

        # Process paid jobs for cash receipts
        logger.info("💰 Checking for paid jobs that need cash receipts...")
        cash_receipt_stats = process_paid_jobs_cash_receipts(db)
        stats["cash_receipt_stats"] = cash_receipt_stats

        return {
            "success": True,
            "message": "Deltek synchronization completed successfully",
            "stats": stats
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Unexpected error during manual Deltek sync: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Unexpected error during Deltek synchronization: {str(e)}"
        )


@router.get("/deltek-scheduler-status", summary="Get Deltek scheduler status")
async def get_deltek_scheduler_status(
    current_user: UserModel = Depends(require_admin)
):
    """
    Get status of the Deltek synchronization scheduler (Admin only)

    **Returns:**
    - Scheduler running status
    - Next scheduled run time
    - List of all scheduled jobs
    """
    try:
        # Get scheduler status
        is_running = scheduler_service.is_running

        # Get Deltek sync job status
        deltek_job_status = scheduler_service.get_job_status('deltek_sync')

        # Get all scheduled jobs
        all_jobs = scheduler_service.list_jobs()

        return {
            "success": True,
            "scheduler_running": is_running,
            "deltek_sync_job": deltek_job_status,
            "all_scheduled_jobs": all_jobs
        }

    except Exception as e:
        logger.error(f"❌ Error getting scheduler status: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting scheduler status: {str(e)}"
        )


@router.get("/deltek-status", summary="Get Deltek integration status (Public)")
async def get_deltek_status():
    """
    Get basic status of the Deltek integration (No authentication required)

    **Returns:**
    - Scheduler running status
    - Next scheduled run time
    - Basic integration health check
    """
    try:
        # Get scheduler status
        is_running = scheduler_service.is_running

        # Get Deltek sync job status
        deltek_job_status = scheduler_service.get_job_status('deltek_sync')

        # Get startup job status
        startup_job_status = scheduler_service.get_job_status('deltek_sync_startup')

        return {
            "success": True,
            "scheduler_running": is_running,
            "deltek_sync_job": deltek_job_status,
            "startup_sync_job": startup_job_status,
            "message": "Deltek integration is active" if is_running else "Deltek integration is not running"
        }

    except Exception as e:
        logger.error(f"❌ Error getting Deltek status: {str(e)}")
        return {
            "success": False,
            "scheduler_running": False,
            "message": f"Error getting Deltek status: {str(e)}"
        }


















