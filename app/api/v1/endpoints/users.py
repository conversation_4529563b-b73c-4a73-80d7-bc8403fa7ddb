from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List
from app.db.database import get_db
from app.schemas.user import User, UserUpdate, UserList
from app.models.user import User as UserModel, UserRole
from app.core.deps import get_current_active_user, require_admin, require_moderator_or_admin
from app.crud.user import get_user_by_id, update_user, get_users, get_users_count

router = APIRouter()

@router.get("/", response_model=UserList)
def get_all_users(
    skip: int = Query(0, ge=0, description="Number of users to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of users to return"),
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_admin)
):
    """
    Get all users with pagination (Admin only)

    **Parameters:**
    - skip: Number of users to skip (for pagination)
    - limit: Maximum number of users to return (1-1000)

    **Returns:**
    - Paginated list of users with total count

    **Permissions:**
    - Only admin users can access this endpoint
    """
    users = get_users(db=db, skip=skip, limit=limit)
    total = get_users_count(db=db)

    return UserList(
        users=users,
        total=total,
        page=skip // limit + 1,
        size=len(users)
    )

@router.get("/me", response_model=User)
def read_users_me(current_user: UserModel = Depends(get_current_active_user)):
    """
    Get current user profile
    """
    return current_user

@router.put("/me", response_model=User)
def update_user_me(
    user_update: UserUpdate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user)
):
    """
    Update current user profile
    """
    # Users can only update their own basic info, not role or verification status
    restricted_update = UserUpdate(
        email=user_update.email,
        username=user_update.username,
        full_name=user_update.full_name,
        password=user_update.password
    )
    
    updated_user = update_user(db, current_user.id, restricted_update)
    if not updated_user:
        raise HTTPException(status_code=404, detail="User not found")
    return updated_user

@router.get("/profile", response_model=User)
def get_user_profile(current_user: UserModel = Depends(get_current_active_user)):
    """
    Get user profile (protected endpoint)
    """
    return current_user

@router.get("/admin-only")
def admin_only_endpoint(current_user: UserModel = Depends(require_admin)):
    """
    Admin-only endpoint
    """
    return {"message": "Hello Admin!", "user": current_user.email}

@router.get("/moderator-or-admin")
def moderator_or_admin_endpoint(current_user: UserModel = Depends(require_moderator_or_admin)):
    """
    Moderator or Admin only endpoint
    """
    return {"message": "Hello Moderator/Admin!", "user": current_user.email, "role": current_user.role}

@router.get("/{user_id}", response_model=User)
def read_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_moderator_or_admin)
):
    """
    Get user by ID (moderator/admin only)
    """
    user = get_user_by_id(db, user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return user

@router.put("/{user_id}", response_model=User)
def update_user_by_id(
    user_id: int,
    user_update: UserUpdate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_admin)
):
    """
    Update user by ID (admin only)
    """
    updated_user = update_user(db, user_id, user_update)
    if not updated_user:
        raise HTTPException(status_code=404, detail="User not found")
    return updated_user
