from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import func, and_
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, Any

from app.db.database import get_db
from app.models.user import User
from app.models.job import Job
from app.models.billing import Billing
from app.core.deps import get_current_active_user, require_admin
from app.models.user import User as UserModel

router = APIRouter()

@router.get("/dashboard", summary="Get dashboard insights")
def get_dashboard_insights(
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_admin)
):
    """
    Get dashboard insights and analytics (Admin only)
    
    Returns:
    - Total number of users
    - Total number of projects (jobs)
    - Total number of billings
    - Total paid amount from current month
    
    **Permissions:**
    - Only admin users can access insights
    """
    try:
        # 1. Get total number of users
        total_users = db.query(func.count(User.id)).scalar()
        
        # 2. Get total number of projects (jobs)
        total_projects = db.query(func.count(Job.id)).scalar()
        
        # 3. Get total number of billings
        total_billings = db.query(func.count(Billing.id)).scalar()
        
        # 4. Get total paid amount from current month
        # Calculate date range for current month
        today = datetime.now()
        first_day_this_month = today.replace(day=1)

        # Query for paid billings from current month
        current_month_paid_amount = db.query(
            func.coalesce(func.sum(Billing.invoice_amount), 0)
        ).filter(
            and_(
                Billing.paid == True,
                Billing.invoice_date >= first_day_this_month,
                Billing.invoice_date <= today
            )
        ).scalar()

        # Convert to float for JSON serialization
        current_month_paid_amount = float(current_month_paid_amount or 0)
        
        return {
            "success": True,
            "data": {
                "total_users": total_users,
                "total_projects": total_projects,
                "total_billings": total_billings,
                "current_month_paid_amount": current_month_paid_amount
            },
            "period": {
                "current_month_start": first_day_this_month.strftime('%Y-%m-%d'),
                "current_month_end": today.strftime('%Y-%m-%d')
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch insights: {str(e)}"
        )


