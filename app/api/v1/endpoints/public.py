from fastapi import APIRouter, Depends
from fastapi.responses import HTMLResponse
from sqlalchemy.orm import Session
import logging

from app.db.database import get_db
from app.core.stripe_service import stripe_service
from app.core.config import settings
from app.models.billing import Billing
from app.models.job import JobStatus
from app.crud.billing import mark_billing_as_paid
from app.core.scheduler import scheduler_service

# Configure logging
logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/payment-success", summary="Payment success page")
async def payment_success(
    session_id: str,
    db: Session = Depends(get_db)
):
    """
    Cool payment success page - shows animated check icon and success message

    **Parameters:**
    - `session_id`: Stripe checkout session ID

    **Returns:**
    - Beautiful HTML page with animated success confirmation

    **Note:**
    - This endpoint is public (no authentication required)
    - Customers are redirected here after completing Stripe payment
    - Automatically updates payment status in background
    - Triggers Deltek sync for immediate cash receipt processing
    """
    try:
        # Trigger Deltek sync for immediate cash receipt processing
        logger.info(f"💰 Payment success page accessed with session_id: {session_id}")
        logger.info("🔄 Triggering Deltek sync for immediate cash receipt processing...")

        try:
            from app.core.scheduler import scheduler_service

            # Run Deltek sync in background to process cash receipts immediately
            # This ensures cash receipts are created right after payment success
            sync_result = scheduler_service.run_deltek_sync()

            if sync_result and sync_result.get("success"):
                logger.info("✅ Deltek sync triggered successfully from payment success page")
            else:
                logger.warning(f"⚠️ Deltek sync had issues: {sync_result.get('message', 'Unknown error') if sync_result else 'No result'}")

        except Exception as e:
            logger.error(f"❌ Error triggering Deltek sync from payment success page: {str(e)}")
            # Don't fail the success page if sync fails

        # Always show success page regardless of status check results
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Payment Successful - DOTec Hub</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
                * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }
                
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    min-height: 100vh;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 20px;
                }
                
                .container {
                    background: white;
                    border-radius: 20px;
                    padding: 60px 40px;
                    text-align: center;
                    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                    max-width: 500px;
                    width: 100%;
                    position: relative;
                    overflow: hidden;
                }
                
                .container::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 5px;
                    background: linear-gradient(90deg, #4CAF50, #45a049);
                }
                
                .check-circle {
                    width: 120px;
                    height: 120px;
                    border-radius: 50%;
                    background: linear-gradient(135deg, #4CAF50, #45a049);
                    margin: 0 auto 30px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    position: relative;
                    animation: scaleIn 0.6s ease-out;
                    box-shadow: 0 10px 30px rgba(76, 175, 80, 0.3);
                }
                
                .check-circle::after {
                    content: '';
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    border-radius: 50%;
                    background: linear-gradient(135deg, #4CAF50, #45a049);
                    animation: pulse 2s infinite;
                    z-index: -1;
                }
                
                .check-icon {
                    color: white;
                    font-size: 50px;
                    font-weight: bold;
                    animation: checkmark 0.8s ease-out 0.3s both;
                }
                
                .title {
                    font-size: 32px;
                    font-weight: 700;
                    color: #2c3e50;
                    margin-bottom: 15px;
                    animation: slideUp 0.6s ease-out 0.4s both;
                }
                
                .message {
                    font-size: 18px;
                    color: #7f8c8d;
                    margin-bottom: 40px;
                    line-height: 1.6;
                    animation: slideUp 0.6s ease-out 0.5s both;
                }
                
                .btn {
                    display: inline-block;
                    padding: 15px 30px;
                    background: linear-gradient(135deg, #667eea, #764ba2);
                    color: white;
                    text-decoration: none;
                    border-radius: 50px;
                    font-weight: 600;
                    font-size: 16px;
                    transition: all 0.3s ease;
                    animation: slideUp 0.6s ease-out 0.6s both;
                    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
                }
                
                .btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
                }
                
                @keyframes scaleIn {
                    0% {
                        transform: scale(0);
                        opacity: 0;
                    }
                    50% {
                        transform: scale(1.1);
                    }
                    100% {
                        transform: scale(1);
                        opacity: 1;
                    }
                }
                
                @keyframes pulse {
                    0% {
                        transform: scale(1);
                        opacity: 0.8;
                    }
                    50% {
                        transform: scale(1.1);
                        opacity: 0.4;
                    }
                    100% {
                        transform: scale(1);
                        opacity: 0.8;
                    }
                }
                
                @keyframes checkmark {
                    0% {
                        transform: scale(0) rotate(45deg);
                        opacity: 0;
                    }
                    100% {
                        transform: scale(1) rotate(0deg);
                        opacity: 1;
                    }
                }
                
                @keyframes slideUp {
                    0% {
                        transform: translateY(30px);
                        opacity: 0;
                    }
                    100% {
                        transform: translateY(0);
                        opacity: 1;
                    }
                }
                
                @media (max-width: 480px) {
                    .container {
                        padding: 40px 20px;
                    }
                    
                    .check-circle {
                        width: 100px;
                        height: 100px;
                    }
                    
                    .check-icon {
                        font-size: 40px;
                    }
                    
                    .title {
                        font-size: 28px;
                    }
                    
                    .message {
                        font-size: 16px;
                    }
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="check-circle">
                    <div class="check-icon">✓</div>
                </div>
                
                <h1 class="title">Payment Successful!</h1>
                
                <p class="message">
                    Your payment has been processed successfully.<br>
                    Thank you for your business!
                </p>
                
                <a href="#" class="btn" onclick="window.close(); return false;">Close</a>
            </div>
        </body>
        </html>
        """
        
        return HTMLResponse(content=html_content)
        
    except Exception as e:
        logger.error(f"❌ Error in payment success page: {str(e)}")
        # Still show success page even if there's an error
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Payment Successful - DOTec Hub</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
                * { margin: 0; padding: 0; box-sizing: border-box; }
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    min-height: 100vh;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 20px;
                }
                .container {
                    background: white;
                    border-radius: 20px;
                    padding: 60px 40px;
                    text-align: center;
                    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                    max-width: 500px;
                    width: 100%;
                }
                .check-circle {
                    width: 120px;
                    height: 120px;
                    border-radius: 50%;
                    background: linear-gradient(135deg, #4CAF50, #45a049);
                    margin: 0 auto 30px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                .check-icon { color: white; font-size: 50px; font-weight: bold; }
                .title { font-size: 32px; font-weight: 700; color: #2c3e50; margin-bottom: 15px; }
                .message { font-size: 18px; color: #7f8c8d; margin-bottom: 40px; line-height: 1.6; }
                .btn {
                    display: inline-block;
                    padding: 15px 30px;
                    background: linear-gradient(135deg, #667eea, #764ba2);
                    color: white;
                    text-decoration: none;
                    border-radius: 50px;
                    font-weight: 600;
                    font-size: 16px;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="check-circle">
                    <div class="check-icon">✓</div>
                </div>
                <h1 class="title">Payment Successful!</h1>
                <p class="message">Your payment has been processed successfully.<br>Thank you for your business!</p>
                <a href="#" class="btn" onclick="window.close(); return false;">Close</a>
            </div>
        </body>
        </html>
        """
        return HTMLResponse(content=html_content)


@router.get("/payment-cancelled", summary="Payment cancelled page")
async def payment_cancelled():
    """Payment cancelled page with cool design"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Payment Cancelled - DOTec Hub</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 20px;
            }
            .container {
                background: white;
                border-radius: 20px;
                padding: 60px 40px;
                text-align: center;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                max-width: 500px;
                width: 100%;
            }
            .x-circle {
                width: 120px;
                height: 120px;
                border-radius: 50%;
                background: linear-gradient(135deg, #ff6b6b, #ee5a24);
                margin: 0 auto 30px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .x-icon { color: white; font-size: 50px; font-weight: bold; }
            .title { font-size: 32px; font-weight: 700; color: #2c3e50; margin-bottom: 15px; }
            .message { font-size: 18px; color: #7f8c8d; margin-bottom: 40px; line-height: 1.6; }
            .btn {
                display: inline-block;
                padding: 15px 30px;
                background: linear-gradient(135deg, #667eea, #764ba2);
                color: white;
                text-decoration: none;
                border-radius: 50px;
                font-weight: 600;
                font-size: 16px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="x-circle">
                <div class="x-icon">✕</div>
            </div>
            <h1 class="title">Payment Cancelled</h1>
            <p class="message">Your payment was cancelled.<br>No charges have been made to your account.</p>
            <a href="#" class="btn" onclick="window.close(); return false;">Close</a>
        </div>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)


@router.get("/deltek-status", summary="Get Deltek integration status (Public)")
async def get_deltek_status():
    """
    Get basic status of the Deltek integration (No authentication required)

    **Returns:**
    - Scheduler running status
    - Next scheduled run time
    - Basic integration health check
    """
    try:
        # Get scheduler status
        is_running = scheduler_service.is_running

        # Get Deltek sync job status
        deltek_job_status = scheduler_service.get_job_status('deltek_sync')

        # Get startup job status
        startup_job_status = scheduler_service.get_job_status('deltek_sync_startup')

        return {
            "success": True,
            "scheduler_running": is_running,
            "deltek_sync_job": deltek_job_status,
            "startup_sync_job": startup_job_status,
            "message": "Deltek integration is active" if is_running else "Deltek integration is not running"
        }

    except Exception as e:
        logger.error(f"❌ Error getting Deltek status: {str(e)}")
        return {
            "success": False,
            "scheduler_running": False,
            "message": f"Error getting Deltek status: {str(e)}"
        }
