from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
import logging
from app.db.database import get_db
from app.schemas.billing import Billing
from app.models.user import User as UserModel
from app.core.deps import get_current_active_user, require_admin
from app.crud.billing import (
    get_billings_by_user_id,
    get_billings,
    get_billing_by_id,
    get_billing_by_invoice_number,
    update_billing
)
from app.schemas.billing import BillingUpdate

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/my-billings", response_model=List[Billing], summary="Get current user's billings")
def get_my_billings(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user)
):
    """
    Get all billing records for the current authenticated user
    
    **Parameters:**
    - `skip`: Number of records to skip (for pagination)
    - `limit`: Maximum number of records to return (1-1000)
    
    **Returns:**
    - List of billing records created by the current user
    
    **Response includes:**
    - Invoice number and details
    - Customer information
    - Invoice amounts and dates
    - Complete job details (name, client, status, deadline, etc.)
    - Job metadata and timestamps
    
    **Use Cases:**
    - View invoices you've generated
    - Track billing history
    - Financial reporting
    - Invoice management
    """
    billings = get_billings_by_user_id(
        db=db, 
        user_id=current_user.id, 
        skip=skip, 
        limit=limit
    )
    return billings

@router.get("/user/{user_id}", response_model=List[Billing], summary="Get billings by user ID (Admin only)")
def get_billings_by_user(
    user_id: int,
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_admin)
):
    """
    Get all billing records for a specific user (Admin only)
    
    **Parameters:**
    - `user_id`: ID of the user to get billings for
    - `skip`: Number of records to skip (for pagination)
    - `limit`: Maximum number of records to return (1-1000)
    
    **Returns:**
    - List of billing records created by the specified user
    
    **Admin Features:**
    - View any user's billing history
    - Monitor invoice generation activity
    - Financial oversight and reporting
    - User activity tracking
    
    **Permissions:**
    - Admin access required
    - Regular users can only see their own billings via `/my-billings`
    """
    billings = get_billings_by_user_id(
        db=db, 
        user_id=user_id, 
        skip=skip, 
        limit=limit
    )
    return billings

@router.get("/", response_model=List[Billing], summary="Get all billings (Admin only)")
def get_all_billings(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_admin)
):
    """
    Get all billing records in the system (Admin only)
    
    **Parameters:**
    - `skip`: Number of records to skip (for pagination)
    - `limit`: Maximum number of records to return (1-1000)
    
    **Returns:**
    - List of all billing records in the system
    
    **Admin Features:**
    - Complete billing overview
    - System-wide financial reporting
    - Invoice audit trail
    - Comprehensive billing management
    
    **Permissions:**
    - Admin access required
    - Provides full system billing visibility
    """
    billings = get_billings(db=db, skip=skip, limit=limit)
    return billings

@router.get("/{billing_id}", response_model=Billing, summary="Get billing by ID")
def get_billing(
    billing_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user)
):
    """
    Get a specific billing record by ID
    
    **Parameters:**
    - `billing_id`: ID of the billing record to retrieve
    
    **Returns:**
    - Detailed billing record information
    
    **Access Control:**
    - Users can only access their own billing records
    - Admins can access any billing record
    
    **Response includes:**
    - Complete invoice details
    - Customer information
    - Full job details (name, client, status, description, deadline)
    - Associated user data
    - Timestamps and amounts
    """
    billing = get_billing_by_id(db=db, billing_id=billing_id)
    
    if not billing:
        raise HTTPException(status_code=404, detail="Billing record not found")
    
    # Check if user can access this billing record
    if current_user.role.value != "admin" and billing.user_id != current_user.id:
        raise HTTPException(
            status_code=403, 
            detail="Not authorized to access this billing record"
        )
    
    return billing

@router.get("/invoice/{invoice_number}", response_model=Billing, summary="Get billing by invoice number")
def get_billing_by_invoice(
    invoice_number: str,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user)
):
    """
    Get a billing record by invoice number
    
    **Parameters:**
    - `invoice_number`: Invoice number to search for (e.g., INV-123-20241220_143022)
    
    **Returns:**
    - Billing record with the specified invoice number
    
    **Use Cases:**
    - Look up invoice by number
    - Customer service inquiries
    - Payment processing
    - Invoice verification
    
    **Access Control:**
    - Users can only access their own billing records
    - Admins can access any billing record
    """
    billing = get_billing_by_invoice_number(db=db, invoice_number=invoice_number)
    
    if not billing:
        raise HTTPException(status_code=404, detail="Invoice not found")
    
    # Check if user can access this billing record
    if current_user.role.value != "admin" and billing.user_id != current_user.id:
        raise HTTPException(
            status_code=403, 
            detail="Not authorized to access this invoice"
        )
    
    return billing

@router.patch("/{billing_id}/mark-paid", response_model=Billing, summary="Mark billing as paid")
def mark_billing_as_paid(
    billing_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user)
):
    """
    Mark a billing record as paid

    **Parameters:**
    - `billing_id`: ID of the billing record to mark as paid

    **Returns:**
    - Updated billing record with paid status set to true

    **Access Control:**
    - Users can only mark their own billing records as paid
    - Admins can mark any billing record as paid

    **Use Cases:**
    - Payment processing completion
    - Manual payment confirmation
    - Invoice status management
    - Financial record keeping

    **Business Logic:**
    - Once marked as paid, new invoice generations for the same job will create new billing records
    - Paid status cannot be reverted through this endpoint (use PATCH for manual updates)
    """
    billing = get_billing_by_id(db=db, billing_id=billing_id)

    if not billing:
        raise HTTPException(status_code=404, detail="Billing record not found")

    # Check if user can access this billing record
    if current_user.role.value != "admin" and billing.user_id != current_user.id:
        raise HTTPException(
            status_code=403,
            detail="Not authorized to modify this billing record"
        )

    # Mark as paid
    billing_update = BillingUpdate(paid=True)
    updated_billing = update_billing(db=db, billing_id=billing_id, billing_update=billing_update)

    if not updated_billing:
        raise HTTPException(status_code=500, detail="Failed to update billing record")

    return updated_billing


@router.post("/{billing_id}/generate-cash-receipt", summary="Generate cash receipt in Deltek")
def generate_cash_receipt(
    billing_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_admin)
):
    """
    Manually generate cash receipt in Deltek for a paid billing record (Admin only)

    **Parameters:**
    - `billing_id`: ID of the billing record to generate cash receipt for

    **Returns:**
    - Cash receipt generation result and updated billing record

    **Requirements:**
    - Billing must be paid
    - Job must be a Deltek import with required fields
    - Cash receipt must not already be generated

    **Use Cases:**
    - Manual cash receipt generation for failed automatic attempts
    - Retry cash receipt creation after Deltek API issues
    - Administrative cash receipt management
    """
    try:
        # Get billing record
        billing = get_billing_by_id(db, billing_id)
        if not billing:
            raise HTTPException(status_code=404, detail="Billing record not found")

        # Check if billing is paid
        if not billing.paid:
            raise HTTPException(status_code=400, detail="Billing must be paid before generating cash receipt")

        # Check if cash receipt already generated
        if billing.is_cash_receipt_generated:
            raise HTTPException(status_code=400, detail="Cash receipt already generated for this billing")

        # Get job details
        job = billing.job
        if not job:
            raise HTTPException(status_code=400, detail="Job not found for billing record")

        # Check if it's a Deltek job with required data
        if not job.is_deltek_import:
            raise HTTPException(status_code=400, detail="Job is not a Deltek import")

        if not job.deltek_invoice_id or not job.deltek_project_number:
            raise HTTPException(status_code=400, detail="Job missing required Deltek data (invoice_id or project_number)")

        # Import Deltek service
        from app.core.deltek_service import deltek_service

        # Prepare billing data for cash receipt
        billing_data = {
            "job_id": job.id,
            "job_name": job.job_name,
            "deltek_project_number": job.deltek_project_number,
            "deltek_invoice_id": job.deltek_invoice_id,
            "amount": float(billing.invoice_amount)
        }

        logger.info(f"💰 Admin manually generating cash receipt for billing {billing_id}")

        # Create cash receipt in Deltek
        cash_receipt_result = deltek_service.create_cash_receipt(billing_data)

        if cash_receipt_result["success"]:
            # Mark cash receipt as generated
            billing.is_cash_receipt_generated = True
            db.commit()
            db.refresh(billing)

            logger.info(f"✅ Cash receipt created successfully for billing {billing_id}")

            return {
                "success": True,
                "message": "Cash receipt generated successfully",
                "billing": billing,
                "cash_receipt_details": {
                    "batch_id": cash_receipt_result.get("batch_id"),
                    "ref_no": cash_receipt_result.get("ref_no"),
                    "deltek_response": cash_receipt_result.get("deltek_response", {})
                }
            }
        else:
            logger.error(f"❌ Failed to create cash receipt for billing {billing_id}: {cash_receipt_result.get('message', 'Unknown error')}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to create cash receipt in Deltek: {cash_receipt_result.get('message', 'Unknown error')}"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Error generating cash receipt for billing {billing_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Unexpected error generating cash receipt: {str(e)}"
        )


@router.get("/cash-receipt-status", summary="Get cash receipt status for all billings")
def get_cash_receipt_status(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_admin)
):
    """
    Get cash receipt generation status for all billing records (Admin only)

    **Parameters:**
    - `skip`: Number of records to skip (default: 0)
    - `limit`: Maximum number of records to return (default: 100)

    **Returns:**
    - List of billing records with cash receipt status
    - Summary statistics

    **Use Cases:**
    - Monitor cash receipt generation status
    - Identify failed cash receipt generations
    - Administrative reporting and management
    """
    try:
        # Get all paid billings with pagination
        paid_billings = db.query(Billing).filter(
            Billing.paid == True
        ).offset(skip).limit(limit).all()

        # Calculate statistics
        total_paid = db.query(Billing).filter(Billing.paid == True).count()
        total_with_receipts = db.query(Billing).filter(
            Billing.paid == True,
            Billing.is_cash_receipt_generated == True
        ).count()
        total_without_receipts = total_paid - total_with_receipts

        # Prepare response data
        billing_status = []
        for billing in paid_billings:
            job = billing.job
            billing_info = {
                "billing_id": billing.id,
                "invoice_number": billing.invoice_number,
                "customer_name": billing.customer_name,
                "amount": float(billing.invoice_amount),
                "paid": billing.paid,
                "is_cash_receipt_generated": billing.is_cash_receipt_generated,
                "job_id": billing.job_id,
                "job_name": job.job_name if job else None,
                "is_deltek_import": job.is_deltek_import if job else False,
                "deltek_invoice_id": job.deltek_invoice_id if job else None,
                "deltek_project_number": job.deltek_project_number if job else None,
                "can_generate_receipt": (
                    billing.paid and
                    not billing.is_cash_receipt_generated and
                    job and
                    job.is_deltek_import and
                    job.deltek_invoice_id and
                    job.deltek_project_number
                ),
                "created_at": billing.created_at,
                "updated_at": billing.updated_at
            }
            billing_status.append(billing_info)

        return {
            "success": True,
            "billings": billing_status,
            "pagination": {
                "skip": skip,
                "limit": limit,
                "total_returned": len(billing_status)
            },
            "statistics": {
                "total_paid_billings": total_paid,
                "total_with_cash_receipts": total_with_receipts,
                "total_without_cash_receipts": total_without_receipts,
                "cash_receipt_coverage": round((total_with_receipts / total_paid * 100) if total_paid > 0 else 0, 2)
            }
        }

    except Exception as e:
        logger.error(f"❌ Error getting cash receipt status: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting cash receipt status: {str(e)}"
        )
