from fastapi import APIRouter
from app.api.v1.endpoints import auth, users, jobs, billing, insights, public, email

api_router = APIRouter()

api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(jobs.router, prefix="/jobs", tags=["jobs"])
api_router.include_router(billing.router, prefix="/billing", tags=["billing"])
api_router.include_router(insights.router, prefix="/insights", tags=["insights"])
api_router.include_router(email.router, prefix="/email", tags=["email"])
api_router.include_router(public.router, prefix="/public", tags=["public"])
