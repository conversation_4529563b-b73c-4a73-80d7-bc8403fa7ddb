"""
Lightweight CSV processing without pandas
"""
import csv
import io
from typing import List, Dict, Any
from datetime import datetime


def parse_amount(value):
    """Parse amount string to float, handling commas and invalid values"""
    try:
        if not value or value == "":
            return None
        return float(str(value).replace(",", "").strip())
    except:
        return None


def parse_date(value):
    """Parse date string to date object"""
    try:
        if not value or value == "":
            return None
        # Try different date formats
        for fmt in ["%m/%d/%Y", "%Y-%m-%d", "%d/%m/%Y"]:
            try:
                return datetime.strptime(str(value), fmt).date()
            except ValueError:
                continue
        return None
    except:
        return None


def process_csv_content(content: bytes) -> List[Dict[str, Any]]:
    """
    Process CSV content and return list of job dictionaries
    
    Args:
        content: CSV file content as bytes
        
    Returns:
        List of dictionaries with job data
    """
    # Decode content
    text_content = content.decode('utf-8-sig')  # Handle BOM
    
    # Parse CSV
    csv_reader = csv.DictReader(io.StringIO(text_content))
    
    jobs = []
    for row in csv_reader:
        # Map CSV columns to job fields
        job_data = {}
        
        # Job name (required)
        job_name = (
            row.get('groupHeader_BillingGroupDetails') or 
            row.get('job_name') or 
            row.get('Job Name') or
            row.get('Project Name')
        )
        
        if not job_name or not job_name.strip():
            continue  # Skip rows without job name
            
        job_data['job_name'] = job_name.strip()
        
        # Client name
        client_name = (
            row.get('detail_ClientName') or 
            row.get('client_name') or
            row.get('Client Name') or
            row.get('Customer')
        )
        if client_name:
            job_data['client_name'] = client_name.strip()
        
        # Job description
        description = (
            row.get('description') or 
            row.get('job_description') or
            row.get('Description') or
            row.get('Project Description')
        )
        if description:
            job_data['job_description'] = description.strip()
        
        # Project deadline
        deadline = (
            row.get('detail_DueDate') or 
            row.get('project_deadline') or
            row.get('Due Date') or
            row.get('Deadline')
        )
        if deadline:
            parsed_date = parse_date(deadline)
            if parsed_date:
                job_data['project_deadline'] = parsed_date
        
        # Contract amount
        amount = (
            row.get('detail_OriginalAmt') or 
            row.get('contract_amount') or
            row.get('Amount') or
            row.get('Total')
        )
        if amount:
            parsed_amount = parse_amount(amount)
            if parsed_amount:
                job_data['contract_amount'] = parsed_amount
        
        # Service type (default to structural_analysis)
        service_type = (
            row.get('service_type') or 
            row.get('Service Type') or
            'structural_analysis'
        )
        job_data['service_type'] = service_type
        
        jobs.append(job_data)
    
    return jobs


def remove_duplicates(jobs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Remove duplicate jobs by job_name, prioritizing records with contract_amount
    """
    # Group by job_name
    job_groups = {}
    for job in jobs:
        job_name = job['job_name']
        if job_name not in job_groups:
            job_groups[job_name] = []
        job_groups[job_name].append(job)
    
    # Select best record from each group
    unique_jobs = []
    for job_name, group in job_groups.items():
        # Sort by contract_amount (records with amounts first)
        group.sort(key=lambda x: (x.get('contract_amount') is None, x.get('contract_amount', 0)), reverse=True)
        unique_jobs.append(group[0])
    
    return unique_jobs
