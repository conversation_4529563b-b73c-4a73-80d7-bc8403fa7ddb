from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from datetime import datetime
import os
import io

class ModernInvoiceGenerator:
    def __init__(self):
        self.styles = getSampleStyleSheet()
        self.setup_custom_styles()
    
    def setup_custom_styles(self):
        # Custom styles for modern look
        self.styles.add(ParagraphStyle(
            name='CustomTitle',
            parent=self.styles['Heading1'],
            fontSize=28,
            textColor=colors.HexColor('#2c3e50'),
            spaceAfter=30,
            alignment=0,
            fontName='Helvetica-Bold'
        ))
        
        self.styles.add(ParagraphStyle(
            name='CompanyName',
            parent=self.styles['Normal'],
            fontSize=16,
            textColor=colors.HexColor('#34495e'),
            fontName='Helvetica-Bold',
            spaceAfter=6
        ))
        
        self.styles.add(ParagraphStyle(
            name='SectionHeader',
            parent=self.styles['Heading2'],
            fontSize=14,
            textColor=colors.HexColor('#2980b9'),
            fontName='Helvetica-Bold',
            spaceAfter=12,
            spaceBefore=20
        ))
        
        self.styles.add(ParagraphStyle(
            name='InfoText',
            parent=self.styles['Normal'],
            fontSize=10,
            textColor=colors.HexColor('#7f8c8d'),
            spaceAfter=4
        ))

    def create_invoice(self, invoice_data, filename="invoice.pdf"):
        """Create a modern PDF invoice"""

        doc = SimpleDocTemplate(filename, pagesize=A4,
                              rightMargin=72, leftMargin=72,
                              topMargin=72, bottomMargin=18)

        # Container for the 'Flowable' objects
        elements = []

        # Add header
        elements.extend(self._create_header(invoice_data))

        # Add company and client info
        elements.extend(self._create_info_section(invoice_data))

        # Add invoice details
        elements.extend(self._create_invoice_details(invoice_data))

        # Add PayPal payment section if URL is provided
        if invoice_data.get('paypal_invoice_url'):
            elements.extend(self._create_paypal_payment_section(invoice_data))

        # Add items table
        elements.extend(self._create_items_table(invoice_data))

        # Add totals
        elements.extend(self._create_totals_section(invoice_data))

        # Add footer (check for PayPal URL in invoice_data)
        paypal_url = invoice_data.get('paypal_invoice_url')
        elements.extend(self._create_footer(paypal_url))

        # Build PDF
        doc.build(elements)
        return filename

    def create_invoice_in_memory(self, invoice_data):
        """Create a PDF invoice in memory and return as bytes"""

        # Create a BytesIO buffer
        buffer = io.BytesIO()

        doc = SimpleDocTemplate(buffer, pagesize=A4,
                              rightMargin=72, leftMargin=72,
                              topMargin=72, bottomMargin=18)

        # Container for the 'Flowable' objects
        elements = []

        # Add header
        elements.extend(self._create_header(invoice_data))

        # Add company and client info
        elements.extend(self._create_info_section(invoice_data))

        # Add invoice details
        elements.extend(self._create_invoice_details(invoice_data))

        # Add PayPal payment section if URL is provided
        if invoice_data.get('paypal_invoice_url'):
            elements.extend(self._create_paypal_payment_section(invoice_data))

        # Add items table
        elements.extend(self._create_items_table(invoice_data))

        # Add totals
        elements.extend(self._create_totals_section(invoice_data))

        # Add footer (check for PayPal URL in invoice_data)
        paypal_url = invoice_data.get('paypal_invoice_url')
        elements.extend(self._create_footer(paypal_url))

        # Build PDF
        doc.build(elements)

        # Get the PDF data
        buffer.seek(0)
        return buffer.getvalue()
        
    def _create_header(self, data):
        elements = []
        
        # Invoice title
        title = Paragraph("INVOICE", self.styles['CustomTitle'])
        elements.append(title)
        elements.append(Spacer(1, 20))
        
        return elements
    
    def _create_info_section(self, data):
        elements = []
        
        # Create a table for company and client info side by side
        company_info = [
            [Paragraph(data['company']['name'], self.styles['CompanyName'])],
            [Paragraph(data['company']['address'], self.styles['InfoText'])],
            [Paragraph(data['company']['city'], self.styles['InfoText'])],
            [Paragraph(f"Phone: {data['company']['phone']}", self.styles['InfoText'])],
            [Paragraph(f"Email: {data['company']['email']}", self.styles['InfoText'])],
            [Paragraph(f"Web: {data['company']['website']}", self.styles['InfoText'])]
        ]
        
        client_info = [
            [Paragraph("BILL TO:", self.styles['SectionHeader'])],
            [Paragraph(data['client']['name'], self.styles['CompanyName'])],
            [''],  # Empty cell for alignment
            [''],  # Empty cell for alignment
            [''],  # Empty cell for alignment
            ['']   # Empty cell for alignment
        ]
        
        # Combine into one table
        info_data = []
        for i in range(len(company_info)):
            info_data.append([company_info[i][0], client_info[i][0]])
        
        info_table = Table(info_data, colWidths=[3*inch, 3*inch])
        info_table.setStyle(TableStyle([
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ('RIGHTPADDING', (0, 0), (-1, -1), 0),
        ]))
        
        elements.append(info_table)
        elements.append(Spacer(1, 30))
        
        return elements
    
    def _create_invoice_details(self, data):
        elements = []
        
        # Invoice details table
        details_data = [
            ['Invoice Number:', data['invoice']['number']],
            ['Invoice Date:', data['invoice']['date']],
            ['Due Date:', data['invoice']['due_date']]
        ]
        
        details_table = Table(details_data, colWidths=[1.5*inch, 2*inch])
        details_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('TEXTCOLOR', (0, 0), (0, -1), colors.HexColor('#2c3e50')),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ]))
        
        elements.append(details_table)
        elements.append(Spacer(1, 30))
        
        return elements
    
    def _create_items_table(self, data):
        elements = []
        
        # Table header
        table_data = [
            ['Description', 'Amount']
        ]
        
        # Add items
        currency = data['invoice']['currency']
        for item in data['items']:
            table_data.append([
                item['description'],
                f"{currency}{item['amount']:.2f}"
            ])
        
        # Create table
        items_table = Table(table_data, colWidths=[5*inch, 1.5*inch])
        
        # Style the table
        items_table.setStyle(TableStyle([
            # Header styling
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#3498db')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            
            # Data rows styling
            ('ALIGN', (0, 1), (0, -1), 'LEFT'),  # Description left-aligned
            ('ALIGN', (1, 1), (-1, -1), 'RIGHT'),  # Amount right-aligned
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.HexColor('#f8f9fa')]),
            ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#bdc3c7')),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('TOPPADDING', (0, 1), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 1), (-1, -1), 8),
        ]))
        
        elements.append(items_table)
        elements.append(Spacer(1, 20))
        
        return elements
    
    def _create_totals_section(self, data):
        elements = []

        currency = data['invoice']['currency']
        total_amount = data['totals']['total']

        # Totals table (right-aligned) - simplified without tax
        totals_data = [
            ['Total:', f"{currency}{total_amount:.2f}"]
        ]

        totals_table = Table(totals_data, colWidths=[1.5*inch, 1.2*inch])
        totals_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.HexColor('#2c3e50')),
            ('BACKGROUND', (0, 0), (-1, -1), colors.HexColor('#ecf0f1')),
            ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#2c3e50')),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('LEFTPADDING', (0, 0), (-1, -1), 10),
            ('RIGHTPADDING', (0, 0), (-1, -1), 10),
        ]))

        # Create a container table to right-align the totals
        container_data = [['', totals_table]]
        container_table = Table(container_data, colWidths=[4.5*inch, 2.7*inch])
        container_table.setStyle(TableStyle([
            ('ALIGN', (1, 0), (1, 0), 'RIGHT'),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ]))

        elements.append(container_table)
        elements.append(Spacer(1, 30))

        return elements

    def _create_paypal_payment_section(self, invoice_data):
        """Create a prominent PayPal payment section"""
        elements = []

        paypal_url = invoice_data.get('paypal_invoice_url')
        if not paypal_url:
            return elements

        # Create a simple but prominent PayPal section
        # Header
        paypal_header = Paragraph(
            "<b>💳 PAY THIS INVOICE WITH PAYPAL</b>",
            self.styles['SectionHeader']
        )

        # Main payment link
        payment_link = Paragraph(
            f'<a href="{paypal_url}" color="blue"><b>🔗 CLICK HERE TO PAY NOW</b></a>',
            self.styles['InfoText']
        )

        # URL display
        url_display = Paragraph(
            f'PayPal Payment URL: <a href="{paypal_url}" color="blue">{paypal_url}</a>',
            self.styles['InfoText']
        )

        # Instructions
        instructions = Paragraph(
            "Secure payment with PayPal • Pay with PayPal account or credit/debit card • Instant confirmation",
            self.styles['InfoText']
        )

        # Create a simple table with blue background
        paypal_data = [
            [paypal_header],
            [payment_link],
            [url_display],
            [instructions]
        ]

        paypal_table = Table(paypal_data, colWidths=[6*inch])
        paypal_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), colors.lightblue),
            ('BOX', (0, 0), (-1, -1), 2, colors.blue),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (-1, -1), 15),
            ('RIGHTPADDING', (0, 0), (-1, -1), 15),
            ('TOPPADDING', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
        ]))

        elements.append(Spacer(1, 20))
        elements.append(paypal_table)
        elements.append(Spacer(1, 20))

        return elements

    def _create_footer(self, paypal_invoice_url=None):
        elements = []

        # Payment information section
        payment_header = Paragraph("Payment Information:", self.styles['SectionHeader'])

        if paypal_invoice_url:
            # Enhanced PayPal payment section
            paypal_header = Paragraph("💳 Pay with PayPal", self.styles['SectionHeader'])

            # Main PayPal payment link
            payment_link = Paragraph(
                f'<a href="{paypal_invoice_url}" color="blue"><b>🔗 Click here to pay your invoice securely with PayPal</b></a>',
                self.styles['InfoText']
            )

            # PayPal URL display
            url_display = Paragraph(
                f'Payment URL: <a href="{paypal_invoice_url}" color="blue">{paypal_invoice_url}</a>',
                self.styles['InfoText']
            )

            # Payment instructions
            instructions = Paragraph(
                "• Click the link above to pay securely with PayPal<br/>"
                "• You can pay with PayPal account or credit/debit card<br/>"
                "• Payment confirmation will be sent automatically<br/>"
                "• For questions, contact <NAME_EMAIL>",
                self.styles['InfoText']
            )

            elements.append(Spacer(1, 30))
            elements.append(paypal_header)
            elements.append(Spacer(1, 10))
            elements.append(payment_link)
            elements.append(Spacer(1, 10))
            elements.append(url_display)
            elements.append(Spacer(1, 10))
            elements.append(instructions)

        else:
            # Fallback to static payment link
            payment_link = Paragraph(
                'Pay online at: <a href="https://dotecengineering.com/pay" color="blue">https://dotecengineering.com/pay</a>',
                self.styles['InfoText']
            )

            elements.append(Spacer(1, 30))
            elements.append(payment_header)
            elements.append(payment_link)

        # Thank you message
        footer_text = Paragraph(
            "Thank you for your business!",
            self.styles['InfoText']
        )

        elements.append(Spacer(1, 20))
        elements.append(footer_text)

        return elements


def create_job_invoice(job_name, client_name, invoice_number, project_deadline, contract_amount, filename=None):
    """
    Create an invoice for a job
    
    Args:
        job_name: Name of the job/project
        client_name: Name of the client
        invoice_number: Invoice number
        project_deadline: Due date for the invoice
        contract_amount: Total amount for the job
        filename: Optional filename for the PDF (auto-generated if not provided)
    
    Returns:
        str: Path to the generated PDF file
    """
    
    if filename is None:
        # Generate filename based on invoice number and timestamp
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"invoice_{invoice_number}_{timestamp}.pdf"

    # Use the filename directly (for backward compatibility)
    filepath = filename
    
    # No tax calculation - use contract amount as total
    total_amount = float(contract_amount) if contract_amount else 0.0
    
    # Sample data structure for the invoice
    invoice_data = {
        'company': {
            'name': 'DOTec Corp.',
            'address': '424 Jefferson St',
            'city': 'St Charles, MO 63301',
            'phone': '+****************',
            'email': '<EMAIL>',
            'website': 'www.dotecengineering.com'
        },
        'client': {
            'name': client_name
        },
        'invoice': {
            'number': invoice_number,
            'date': datetime.now().strftime('%Y-%m-%d'),
            'due_date': project_deadline,
            'currency': '$'
        },
        'items': [
            {
                'description': job_name,
                'amount': total_amount
            }
        ],
        'totals': {
            'total': total_amount
        }
    }
    
    # Create invoice generator and generate PDF
    generator = ModernInvoiceGenerator()
    generator.create_invoice(invoice_data, filepath)
    
    return filepath


def create_job_invoice_in_memory(job_name, client_name, invoice_number, project_deadline, contract_amount, paypal_invoice_url=None):
    """
    Create an invoice for a job in memory and return PDF bytes

    Args:
        job_name: Name of the job/project
        client_name: Name of the client
        invoice_number: Invoice number
        project_deadline: Due date for the invoice
        contract_amount: Total amount for the job
        paypal_invoice_url: Optional PayPal invoice URL for payment

    Returns:
        bytes: PDF file content as bytes
    """

    # No tax calculation - use contract amount as total
    total_amount = float(contract_amount) if contract_amount else 0.0

    # Sample data structure for the invoice
    invoice_data = {
        'company': {
            'name': 'DOTec Corp.',
            'address': '424 Jefferson St',
            'city': 'St Charles, MO 63301',
            'phone': '+****************',
            'email': '<EMAIL>',
            'website': 'www.dotecengineering.com'
        },
        'client': {
            'name': client_name
        },
        'invoice': {
            'number': invoice_number,
            'date': datetime.now().strftime('%Y-%m-%d'),
            'due_date': project_deadline,
            'currency': '$'
        },
        'items': [
            {
                'description': job_name,
                'amount': total_amount
            }
        ],
        'totals': {
            'total': total_amount
        }
    }

    # Add PayPal invoice URL if provided
    if paypal_invoice_url:
        invoice_data['paypal_invoice_url'] = paypal_invoice_url

    # Create invoice generator and generate PDF in memory
    generator = ModernInvoiceGenerator()
    pdf_bytes = generator.create_invoice_in_memory(invoice_data)

    return pdf_bytes
