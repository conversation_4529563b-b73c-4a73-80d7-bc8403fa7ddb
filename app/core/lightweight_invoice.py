"""
Lightweight invoice generation using HTML to PDF conversion
This avoids the heavy reportlab dependency
"""
import io
from datetime import datetime
from typing import Optional


def create_invoice_html(
    job_name: str,
    client_name: str,
    invoice_number: str,
    project_deadline: Optional[str],
    contract_amount: float,
    stripe_checkout_url: Optional[str] = None
) -> str:
    """
    Create invoice HTML content

    Args:
        job_name: Name of the job/project
        client_name: Name of the client
        invoice_number: Invoice number
        project_deadline: Due date for the invoice
        contract_amount: Total amount for the job
        stripe_checkout_url: Optional Stripe checkout URL for ACH payment

    Returns:
        HTML string for the invoice
    """
    
    # Format amount
    total_amount = float(contract_amount) if contract_amount else 0.0
    formatted_amount = f"${total_amount:,.2f}"

    # Format dates
    current_date = datetime.now().strftime('%Y-%m-%d')
    due_date = project_deadline if project_deadline else "Upon completion"
    
    # Payment sections
    payment_section = ""

    # Stripe payment section
    if stripe_checkout_url:
        payment_section += f"""
        <div class="payment-section">
            <h3>Payment Information</h3>
            <p><strong>Pay Online with Stripe:</strong></p>
            <a href="{stripe_checkout_url}" class="payment-button stripe-button" target="_blank">
                Pay Securely Online
            </a>
            <p class="payment-note">Click the button above to pay securely</p>
            <p class="payment-note"><em>Multiple payment methods available: Credit/Debit Cards and Bank Transfers</em></p>
        </div>
        """

    # Default payment section if no online payment options
    if not stripe_checkout_url:
        payment_section = """
        <div class="payment-section">
            <h3>Payment Information</h3>
            <p><strong>Payment Methods:</strong></p>
            <ul>
                <li>Check payable to: DOTec Corp.</li>
                <li>Wire Transfer: Contact us for banking details</li>
                <li>ACH Transfer: Contact us for banking details</li>
            </ul>
            <p class="payment-note">Please include invoice number in payment reference</p>
        </div>
        """
    
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Invoice {invoice_number}</title>
        <style>
            body {{
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 20px;
                color: #333;
                line-height: 1.6;
            }}
            .invoice-container {{
                max-width: 800px;
                margin: 0 auto;
                background: white;
                padding: 30px;
                border: 1px solid #ddd;
            }}
            .header {{
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 30px;
                border-bottom: 2px solid #0066cc;
                padding-bottom: 20px;
            }}
            .company-info {{
                flex: 1;
            }}
            .company-name {{
                font-size: 24px;
                font-weight: bold;
                color: #0066cc;
                margin-bottom: 10px;
            }}
            .company-details {{
                font-size: 14px;
                color: #666;
            }}
            .invoice-title {{
                text-align: right;
                flex: 1;
            }}
            .invoice-title h1 {{
                font-size: 36px;
                color: #0066cc;
                margin: 0;
            }}
            .invoice-meta {{
                display: flex;
                justify-content: space-between;
                margin-bottom: 30px;
            }}
            .client-info, .invoice-info {{
                flex: 1;
            }}
            .client-info {{
                margin-right: 20px;
            }}
            .section-title {{
                font-weight: bold;
                color: #0066cc;
                margin-bottom: 10px;
                font-size: 16px;
            }}
            .invoice-table {{
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 30px;
            }}
            .invoice-table th,
            .invoice-table td {{
                padding: 12px;
                text-align: left;
                border-bottom: 1px solid #ddd;
            }}
            .invoice-table th {{
                background-color: #f8f9fa;
                font-weight: bold;
                color: #0066cc;
            }}
            .amount-cell {{
                text-align: right;
                font-weight: bold;
            }}
            .total-section {{
                text-align: right;
                margin-bottom: 30px;
            }}
            .total-row {{
                display: flex;
                justify-content: flex-end;
                margin-bottom: 10px;
            }}
            .total-label {{
                width: 150px;
                text-align: right;
                margin-right: 20px;
                font-weight: bold;
            }}
            .total-amount {{
                width: 100px;
                text-align: right;
                font-weight: bold;
                font-size: 18px;
                color: #0066cc;
            }}
            .payment-section {{
                background-color: #f8f9fa;
                padding: 20px;
                border-radius: 5px;
                margin-top: 30px;
                text-align: center;
            }}
            .payment-button {{
                display: inline-block;
                color: white;
                padding: 12px 24px;
                text-decoration: none;
                border-radius: 5px;
                font-weight: bold;
                margin: 10px 5px;
                min-width: 200px;
                text-align: center;
            }}
            .stripe-button {{
                background-color: #635bff;
            }}
            .stripe-button:hover {{
                background-color: #5a52e8;
            }}
            .payment-note {{
                font-size: 12px;
                color: #666;
                margin-top: 10px;
            }}
            .footer {{
                margin-top: 40px;
                padding-top: 20px;
                border-top: 1px solid #ddd;
                text-align: center;
                font-size: 12px;
                color: #666;
            }}
            @media print {{
                body {{ margin: 0; }}
                .invoice-container {{ border: none; box-shadow: none; }}
            }}
        </style>
    </head>
    <body>
        <div class="invoice-container">
            <div class="header">
                <div class="company-info">
                    <div class="company-name">DOTec Corp.</div>
                    <div class="company-details">
                        St Charles, MO 63301,<br>
                        118 N 2nd St<br>
                        Phone: +****************<br>
                        Email: <EMAIL><br>
                        Website: www.dotecengineering.com
                    </div>
                </div>
                <div class="invoice-title">
                    <h1>INVOICE</h1>
                </div>
            </div>
            
            <div class="invoice-meta">
                <div class="client-info">
                    <div class="section-title">Bill To:</div>
                    <div>{client_name}</div>
                </div>
                <div class="invoice-info">
                    <div class="section-title">Invoice Details:</div>
                    <div><strong>Invoice #:</strong> {invoice_number}</div>
                    <div><strong>Date:</strong> {current_date}</div>
                    <div><strong>Due Date:</strong> {due_date}</div>
                </div>
            </div>
            
            <table class="invoice-table">
                <thead>
                    <tr>
                        <th>Description</th>
                        <th class="amount-cell">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>{job_name}</td>
                        <td class="amount-cell">{formatted_amount}</td>
                    </tr>
                </tbody>
            </table>
            
            <div class="total-section">
                <div class="total-row">
                    <div class="total-label">Total:</div>
                    <div class="total-amount">{formatted_amount}</div>
                </div>
            </div>
            
            {payment_section}
            
            <div class="footer">
                <p>Thank you for your business!</p>
                <p>For questions about this invoice, please contact <NAME_EMAIL></p>
            </div>
        </div>
    </body>
    </html>
    """

    return html_content


def create_job_invoice_in_memory(
    job_name: str,
    client_name: str,
    invoice_number: str,
    project_deadline: Optional[str],
    contract_amount: float,
    stripe_checkout_url: Optional[str] = None
) -> bytes:
    """
    Create an invoice in memory and return HTML bytes

    For production, you would convert this HTML to PDF using a service like:
    - wkhtmltopdf
    - Puppeteer
    - WeasyPrint (lighter than reportlab)

    For now, returns HTML that can be displayed in browser or converted to PDF
    """
    try:
        html_content = create_invoice_html(
            job_name=job_name,
            client_name=client_name,
            invoice_number=invoice_number,
            project_deadline=project_deadline,
            contract_amount=contract_amount,
            stripe_checkout_url=stripe_checkout_url
        )

        if html_content is None:
            raise ValueError("HTML content generation returned None")

        return html_content.encode('utf-8')
    except Exception as e:
        # Log the error and return a simple error page
        print(f"Error generating invoice HTML: {str(e)}")
        error_html = f"""
        <!DOCTYPE html>
        <html>
        <head><title>Invoice Generation Error</title></head>
        <body>
            <h1>Invoice Generation Error</h1>
            <p>Error: {str(e)}</p>
            <p>Invoice: {invoice_number}</p>
            <p>Job: {job_name}</p>
            <p>Client: {client_name}</p>
        </body>
        </html>
        """
        return error_html.encode('utf-8')


def create_job_invoice(
    job_name: str,
    client_name: str,
    invoice_number: str,
    project_deadline: Optional[str],
    contract_amount: float,
    filename: str,
    stripe_checkout_url: Optional[str] = None
) -> str:
    """
    Create an invoice file and return the filename

    Args:
        job_name: Name of the job/project
        client_name: Name of the client
        invoice_number: Invoice number
        project_deadline: Due date for the invoice
        contract_amount: Total amount for the job
        filename: Output filename
        stripe_checkout_url: Optional Stripe checkout URL for ACH payment

    Returns:
        str: Path to the created file
    """
    html_content = create_invoice_html(
        job_name=job_name,
        client_name=client_name,
        invoice_number=invoice_number,
        project_deadline=project_deadline,
        contract_amount=contract_amount,
        stripe_checkout_url=stripe_checkout_url
    )
    
    # Save as HTML file (can be converted to PDF later)
    html_filename = filename.replace('.pdf', '.html')
    with open(html_filename, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    return html_filename
