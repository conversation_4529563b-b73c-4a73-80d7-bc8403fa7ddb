import os
import uuid
from typing import List, Optional
from fastapi import UploadFile, HTTPException
import aiofiles
from pathlib import Path

# Configuration
UPLOAD_DIR = "uploads/job_documents"
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
ALLOWED_EXTENSIONS = {
    ".pdf", ".doc", ".docx", ".txt", ".jpg", ".jpeg", ".png", ".gif", 
    ".xls", ".xlsx", ".ppt", ".pptx", ".zip", ".rar", ".dwg", ".dxf"
}

def ensure_upload_dir():
    """Ensure upload directory exists"""
    Path(UPLOAD_DIR).mkdir(parents=True, exist_ok=True)

def validate_file(file: UploadFile) -> bool:
    """Validate file type and size"""
    if not file.filename:
        return False
    
    # Check file extension
    file_ext = Path(file.filename).suffix.lower()
    if file_ext not in ALLOWED_EXTENSIONS:
        raise HTTPException(
            status_code=400, 
            detail=f"File type {file_ext} not allowed. Allowed types: {', '.join(ALLOWED_EXTENSIONS)}"
        )
    
    return True

async def save_upload_file(file: UploadFile) -> dict:
    """Save uploaded file and return file info"""
    validate_file(file)
    ensure_upload_dir()
    
    # Generate unique filename
    file_ext = Path(file.filename).suffix.lower()
    unique_filename = f"{uuid.uuid4()}{file_ext}"
    file_path = Path(UPLOAD_DIR) / unique_filename
    
    # Check file size
    content = await file.read()
    if len(content) > MAX_FILE_SIZE:
        raise HTTPException(
            status_code=400,
            detail=f"File size exceeds maximum allowed size of {MAX_FILE_SIZE // (1024*1024)}MB"
        )
    
    # Save file
    async with aiofiles.open(file_path, 'wb') as f:
        await f.write(content)
    
    return {
        "original_filename": file.filename,
        "stored_filename": unique_filename,
        "file_path": str(file_path),
        "file_size": len(content),
        "content_type": file.content_type
    }

async def save_multiple_files(files: List[UploadFile]) -> List[dict]:
    """Save multiple uploaded files"""
    if len(files) > 10:  # Limit number of files
        raise HTTPException(status_code=400, detail="Maximum 10 files allowed")
    
    file_infos = []
    for file in files:
        if file.filename:  # Skip empty files
            file_info = await save_upload_file(file)
            file_infos.append(file_info)
    
    return file_infos

def delete_file(file_path: str) -> bool:
    """Delete a file from storage"""
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            return True
        return False
    except Exception:
        return False

def delete_job_documents(documents: List[dict]) -> None:
    """Delete all documents associated with a job"""
    if not documents:
        return
    
    for doc in documents:
        if isinstance(doc, dict) and "file_path" in doc:
            delete_file(doc["file_path"])
