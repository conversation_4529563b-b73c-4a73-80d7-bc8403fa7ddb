"""
Stripe service for handling ACH (e-check) payments and invoicing
"""

import stripe
import logging
from typing import Dict, Any, Optional
from decimal import Decimal
from datetime import datetime, timedelta

from app.crud.billing import mark_billing_as_paid
from app.core.config import settings

logger = logging.getLogger(__name__)


class StripeService:
    """Service class for Stripe ACH payment operations"""
    
    def __init__(self):
        """Initialize Stripe with configuration"""
        self.configure_stripe()
    
    def configure_stripe(self):
        """Configure Stripe with API key"""
        stripe.api_key = settings.STRIPE_SECRET_KEY
        logger.info(f"Stripe configured in {settings.STRIPE_MODE} mode")
    
    def create_ach_payment_intent(
        self,
        amount: Decimal,
        customer_email: str,
        customer_name: str,
        invoice_number: str,
        job_name: str,
        currency: str = "usd"
    ) -> Dict[str, Any]:
        """
        Create a Stripe Payment Intent for ACH payments
        
        Args:
            amount: Payment amount in dollars
            customer_email: Customer's email address
            customer_name: Customer's name
            invoice_number: Internal invoice number
            job_name: Description of the job/service
            currency: Currency code (default: usd)
            
        Returns:
            Dict containing Stripe payment intent details or error information
        """
        try:
            # Convert amount to cents (Stripe uses cents)
            amount_cents = int(amount * 100)
            
            # Create or retrieve customer
            customer_result = self._get_or_create_customer(customer_email, customer_name)
            if not customer_result["success"]:
                return customer_result
            
            customer_id = customer_result["customer_id"]
            
            # Create payment intent with ACH payment method types
            payment_intent = stripe.PaymentIntent.create(
                amount=amount_cents,
                currency=currency,
                customer=customer_id,
                payment_method_types=['us_bank_account'],  # ACH/e-check payment method
                metadata={
                    'invoice_number': invoice_number,
                    'job_name': job_name,
                    'customer_name': customer_name,
                    'payment_type': 'ach'
                },
                description=f"Invoice {invoice_number} - {job_name}",
                # ACH payments typically take 3-5 business days
                payment_method_options={
                    'us_bank_account': {
                        'verification_method': 'automatic'  # or 'instant' for instant verification
                    }
                }
            )
            
            logger.info(f"Stripe ACH Payment Intent created: {payment_intent.id}")
            
            return {
                "success": True,
                "payment_intent_id": payment_intent.id,
                "client_secret": payment_intent.client_secret,
                "customer_id": customer_id,
                "amount": amount,
                "currency": currency,
                "status": payment_intent.status
            }
            
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error creating ACH payment intent: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to create Stripe ACH payment intent: {str(e)}"
            }
        except Exception as e:
            logger.error(f"Exception creating ACH payment intent: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": "Exception occurred while creating ACH payment intent"
            }
    
    def _get_or_create_customer(self, email: str, name: str) -> Dict[str, Any]:
        """
        Get existing customer or create new one
        
        Args:
            email: Customer email
            name: Customer name
            
        Returns:
            Dict with customer information
        """
        try:
            # Search for existing customer by email
            customers = stripe.Customer.list(email=email, limit=1)
            
            if customers.data:
                customer = customers.data[0]
                logger.info(f"Found existing Stripe customer: {customer.id}")
                return {
                    "success": True,
                    "customer_id": customer.id,
                    "existing_customer": True
                }
            else:
                # Create new customer
                customer = stripe.Customer.create(
                    email=email,
                    name=name,
                    metadata={
                        'source': 'dotec_hub',
                        'created_at': datetime.now().isoformat()
                    }
                )
                logger.info(f"Created new Stripe customer: {customer.id}")
                return {
                    "success": True,
                    "customer_id": customer.id,
                    "existing_customer": False
                }
                
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error with customer: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to handle Stripe customer: {str(e)}"
            }
    
    def get_payment_intent_status(self, payment_intent_id: str) -> Dict[str, Any]:
        """
        Get the current status of a payment intent
        
        Args:
            payment_intent_id: Stripe payment intent ID
            
        Returns:
            Dict containing payment intent status information
        """
        try:
            payment_intent = stripe.PaymentIntent.retrieve(payment_intent_id)
            
            return {
                "success": True,
                "payment_intent_id": payment_intent_id,
                "status": payment_intent.status,
                "amount": payment_intent.amount / 100,  # Convert from cents
                "currency": payment_intent.currency,
                "metadata": payment_intent.metadata,
                "is_paid": payment_intent.status == 'succeeded'
            }
            
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error retrieving payment intent: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to retrieve payment intent: {str(e)}"
            }
    
    def create_setup_intent_for_ach(self, customer_id: str) -> Dict[str, Any]:
        """
        Create a Setup Intent for saving ACH payment method for future use
        
        Args:
            customer_id: Stripe customer ID
            
        Returns:
            Dict containing setup intent details
        """
        try:
            setup_intent = stripe.SetupIntent.create(
                customer=customer_id,
                payment_method_types=['us_bank_account'],
                usage='off_session'  # For future payments
            )
            
            return {
                "success": True,
                "setup_intent_id": setup_intent.id,
                "client_secret": setup_intent.client_secret,
                "status": setup_intent.status
            }
            
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error creating setup intent: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to create setup intent: {str(e)}"
            }
    
    def get_payment_url(self, payment_intent_id: str, return_url: str) -> str:
        """
        Generate a payment URL for ACH payment

        Args:
            payment_intent_id: Stripe payment intent ID
            return_url: URL to redirect after payment

        Returns:
            Payment URL string
        """
        # For ACH payments, we typically need to create a custom payment page
        # or use Stripe's hosted payment page
        base_url = "https://checkout.stripe.com/pay" if settings.STRIPE_MODE == "live" else "https://checkout.stripe.com/pay"
        return f"{base_url}#{payment_intent_id}?return_url={return_url}"

    def create_checkout_session_for_cards(
        self,
        amount: Decimal,
        customer_email: str,
        customer_name: str,
        invoice_number: str,
        job_name: str,
        success_url: str,
        cancel_url: str,
        currency: str = "usd"
    ) -> Dict[str, Any]:
        """
        Create a Stripe Checkout Session for credit/debit card payments

        Args:
            amount: Payment amount in dollars
            customer_email: Customer's email address
            customer_name: Customer's name
            invoice_number: Internal invoice number
            job_name: Description of the job/service
            success_url: URL to redirect after successful payment
            cancel_url: URL to redirect after cancelled payment
            currency: Currency code (default: usd)

        Returns:
            Dict containing checkout session details or error information
        """
        try:
            # Convert amount to cents for Stripe
            amount_cents = int(float(amount) * 100)

            logger.info(f"Creating Stripe checkout session for card payment: {invoice_number} - ${amount}")

            # Create checkout session for card payments
            checkout_session = stripe.checkout.Session.create(
                payment_method_types=['card'],  # Credit/debit cards only
                line_items=[{
                    'price_data': {
                        'currency': currency,
                        'product_data': {
                            'name': f'Invoice {invoice_number}',
                            'description': f'Payment for: {job_name}',
                        },
                        'unit_amount': amount_cents,
                    },
                    'quantity': 1,
                }],
                mode='payment',
                customer_email=customer_email,
                success_url=success_url,
                cancel_url=cancel_url,
                metadata={
                    'invoice_number': invoice_number,
                    'customer_name': customer_name,
                    'job_name': job_name,
                    'payment_type': 'card'
                },
                payment_intent_data={
                    'metadata': {
                        'invoice_number': invoice_number,
                        'customer_name': customer_name,
                        'job_name': job_name,
                        'payment_type': 'card'
                    }
                }
            )

            logger.info(f"Stripe card checkout session created: {checkout_session.id}")

            return {
                "success": True,
                "checkout_session_id": checkout_session.id,
                "checkout_url": checkout_session.url,
                "payment_intent_id": checkout_session.payment_intent,
                "amount": amount,
                "currency": currency,
                "payment_type": "card"
            }

        except stripe.error.StripeError as e:
            logger.error(f"Stripe error creating card checkout session: {str(e)}")
            return {
                "success": False,
                "error": f"Stripe error: {str(e)}",
                "error_type": "stripe_error"
            }
        except Exception as e:
            logger.error(f"Unexpected error creating card checkout session: {str(e)}")
            return {
                "success": False,
                "error": f"Unexpected error: {str(e)}",
                "error_type": "unexpected_error"
            }

    def create_checkout_session_for_ach(
        self,
        amount: Decimal,
        customer_email: str,
        customer_name: str,
        invoice_number: str,
        job_name: str,
        success_url: str,
        cancel_url: str,
        currency: str = "usd"
    ) -> Dict[str, Any]:
        """
        Create a Stripe Checkout Session for ACH payments
        This provides a hosted payment page for easier integration

        Args:
            amount: Payment amount in dollars
            customer_email: Customer's email address
            customer_name: Customer's name
            invoice_number: Internal invoice number
            job_name: Description of the job/service
            success_url: URL to redirect on successful payment
            cancel_url: URL to redirect on cancelled payment
            currency: Currency code (default: usd)

        Returns:
            Dict containing checkout session details or error information
        """
        try:
            # Convert amount to cents (Stripe uses cents)
            amount_cents = int(amount * 100)

            # Create checkout session
            checkout_session = stripe.checkout.Session.create(
                payment_method_types=['us_bank_account'],  # ACH/e-check
                line_items=[{
                    'price_data': {
                        'currency': currency,
                        'product_data': {
                            'name': f"Invoice {invoice_number}",
                            'description': job_name,
                        },
                        'unit_amount': amount_cents,
                    },
                    'quantity': 1,
                }],
                mode='payment',
                customer_email=customer_email,
                success_url=success_url,
                cancel_url=cancel_url,
                metadata={
                    'invoice_number': invoice_number,
                    'job_name': job_name,
                    'customer_name': customer_name,
                    'payment_type': 'ach'
                },
                payment_intent_data={
                    'metadata': {
                        'invoice_number': invoice_number,
                        'job_name': job_name,
                        'customer_name': customer_name,
                        'payment_type': 'ach'
                    }
                }
            )

            logger.info(f"Stripe ACH Checkout Session created: {checkout_session.id}")

            return {
                "success": True,
                "checkout_session_id": checkout_session.id,
                "checkout_url": checkout_session.url,
                "payment_intent_id": checkout_session.payment_intent,
                "amount": amount,
                "currency": currency
            }

        except stripe.error.StripeError as e:
            logger.error(f"Stripe error creating ACH checkout session: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to create Stripe ACH checkout session: {str(e)}"
            }
        except Exception as e:
            logger.error(f"Exception creating ACH checkout session: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": "Exception occurred while creating ACH checkout session"
            }

    def create_checkout_session_for_all_methods(
        self,
        amount: Decimal,
        customer_email: str,
        customer_name: str,
        invoice_number: str,
        job_name: str,
        success_url: str,
        cancel_url: str,
        currency: str = "usd"
    ) -> Dict[str, Any]:
        """
        Create a Stripe Checkout Session supporting both card and ACH payments

        Args:
            amount: Payment amount in dollars
            customer_email: Customer's email address
            customer_name: Customer's name
            invoice_number: Internal invoice number
            job_name: Description of the job/service
            success_url: URL to redirect after successful payment
            cancel_url: URL to redirect after cancelled payment
            currency: Currency code (default: usd)

        Returns:
            Dict containing checkout session details or error information
        """
        try:
            # Convert amount to cents for Stripe
            amount_cents = int(float(amount) * 100)

            logger.info(f"Creating Stripe checkout session for all payment methods: {invoice_number} - ${amount}")

            # Create checkout session supporting both cards and ACH
            checkout_session = stripe.checkout.Session.create(
                payment_method_types=['card', 'us_bank_account'],  # Both cards and ACH
                line_items=[{
                    'price_data': {
                        'currency': currency,
                        'product_data': {
                            'name': f'Invoice {invoice_number}',
                            'description': f'Payment for: {job_name}',
                        },
                        'unit_amount': amount_cents,
                    },
                    'quantity': 1,
                }],
                mode='payment',
                customer_email=customer_email,
                success_url=success_url,
                cancel_url=cancel_url,
                metadata={
                    'invoice_number': invoice_number,
                    'customer_name': customer_name,
                    'job_name': job_name,
                    'payment_type': 'all_methods'
                },
                payment_intent_data={
                    'metadata': {
                        'invoice_number': invoice_number,
                        'customer_name': customer_name,
                        'job_name': job_name,
                        'payment_type': 'all_methods'
                    }
                }
            )

            logger.info(f"Stripe all-methods checkout session created: {checkout_session.id}")

            return {
                "success": True,
                "checkout_session_id": checkout_session.id,
                "checkout_url": checkout_session.url,
                "payment_intent_id": checkout_session.payment_intent,
                "amount": amount,
                "currency": currency,
                "payment_type": "all_methods"
            }

        except stripe.error.StripeError as e:
            logger.error(f"Stripe error creating all-methods checkout session: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to create Stripe all-methods checkout session: {str(e)}"
            }
        except Exception as e:
            logger.error(f"Exception creating all-methods checkout session: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": "Exception occurred while creating all-methods checkout session"
            }

    def confirm_payment_intent(self, payment_intent_id: str, payment_method_id: str) -> Dict[str, Any]:
        """
        Confirm a payment intent with a payment method

        Args:
            payment_intent_id: Stripe payment intent ID
            payment_method_id: Stripe payment method ID

        Returns:
            Dict containing confirmation result
        """
        try:
            payment_intent = stripe.PaymentIntent.confirm(
                payment_intent_id,
                payment_method=payment_method_id
            )

            return {
                "success": True,
                "payment_intent_id": payment_intent.id,
                "status": payment_intent.status,
                "amount": payment_intent.amount / 100,
                "currency": payment_intent.currency
            }

        except stripe.error.StripeError as e:
            logger.error(f"Stripe error confirming payment intent: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to confirm payment intent: {str(e)}"
            }

    def get_checkout_session_status(self, checkout_session_id: str) -> Dict[str, Any]:
        """
        Get the status of a Stripe checkout session

        Args:
            checkout_session_id: Stripe checkout session ID

        Returns:
            Dict containing session status and payment information
        """
        try:
            logger.info(f"Retrieving checkout session status: {checkout_session_id}")

            # Retrieve the checkout session
            session = stripe.checkout.Session.retrieve(checkout_session_id)

            payment_status = session.payment_status
            payment_intent_id = session.payment_intent
            amount_total = session.amount_total / 100 if session.amount_total else 0  # Convert from cents

            # Determine if payment is completed
            is_paid = payment_status == 'paid'

            logger.info(f"Checkout session {checkout_session_id} status: {payment_status}, paid: {is_paid}")

            result = {
                "success": True,
                "checkout_session_id": checkout_session_id,
                "payment_status": payment_status,
                "is_paid": is_paid,
                "payment_intent_id": payment_intent_id,
                "amount_total": amount_total,
                "customer_email": session.customer_details.email if session.customer_details else None,
                "session_status": session.status,
                "url": session.url
            }

            # If there's a payment intent, get additional details
            if payment_intent_id and is_paid:
                payment_intent_status = self.get_payment_intent_status(payment_intent_id)
                if payment_intent_status["success"]:
                    result["payment_intent_details"] = payment_intent_status

            return result

        except stripe.error.InvalidRequestError as e:
            logger.error(f"Invalid checkout session ID: {checkout_session_id} - {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": f"Invalid checkout session ID: {checkout_session_id}"
            }
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error retrieving checkout session: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to retrieve checkout session from Stripe"
            }
        except Exception as e:
            logger.error(f"Exception retrieving checkout session status: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": "Exception occurred while retrieving checkout session status"
            }

    def check_and_update_pending_payments(self, db_session) -> Dict[str, Any]:
        """
        Check all pending payments and update their status
        This method can be called periodically or on each request

        Args:
            db_session: Database session

        Returns:
            Dict containing update results
        """
        try:
            from app.crud.billing import get_unpaid_billings_with_stripe
            from app.crud.job import update_job_status_on_payment, get_job_by_id
            from app.models.job import JobStatus

            logger.info("🔍 Checking pending Stripe payments...")

            # Get all unpaid billings with Stripe session or payment intent IDs
            unpaid_billings = get_unpaid_billings_with_stripe(db_session)

            results = {
                "total_checked": len(unpaid_billings),
                "newly_paid": 0,
                "still_pending": 0,
                "errors": 0,
                "updated_payments": [],
                "completed_jobs": [],
                "error_details": []
            }

            for billing in unpaid_billings:
                try:
                    payment_completed = False
                    payment_details = {}

                    logger.info(f"🔍 Checking billing {billing.id} - Invoice: {billing.invoice_number}")
                    logger.info(f"   Checkout Session ID: {billing.stripe_checkout_session_id}")
                    logger.info(f"   Payment Intent ID: {billing.stripe_payment_intent_id}")

                    # Check checkout session status if available
                    if billing.stripe_checkout_session_id:
                        logger.info(f"   Checking checkout session: {billing.stripe_checkout_session_id}")
                        session_status = self.get_checkout_session_status(billing.stripe_checkout_session_id)
                        logger.info(f"   Session status result: {session_status}")

                        if session_status["success"] and session_status["is_paid"]:
                            payment_completed = True
                            payment_details = session_status
                            logger.info(f"✅ Payment completed via checkout session: {billing.stripe_checkout_session_id}")
                        else:
                            logger.info(f"   Session not paid yet: {session_status.get('payment_status', 'unknown')}")

                    # Check payment intent status if available and not already confirmed
                    elif billing.stripe_payment_intent_id and not payment_completed:
                        logger.info(f"   Checking payment intent: {billing.stripe_payment_intent_id}")
                        intent_status = self.get_payment_intent_status(billing.stripe_payment_intent_id)
                        logger.info(f"   Intent status result: {intent_status}")

                        if intent_status["success"] and intent_status["is_paid"]:
                            payment_completed = True
                            payment_details = intent_status
                            logger.info(f"✅ Payment completed via payment intent: {billing.stripe_payment_intent_id}")
                        else:
                            logger.info(f"   Intent not paid yet: {intent_status.get('status', 'unknown')}")
                    else:
                        logger.info(f"   No Stripe IDs found for billing {billing.id}")
                        results["still_pending"] += 1
                        continue

                    if payment_completed:
                        logger.info(f"💰 Processing payment completion for billing {billing.id}")

                        # Mark billing as paid
                        updated_billing = mark_billing_as_paid(db_session, billing.id)
                        if updated_billing:
                            logger.info(f"✅ Billing {billing.id} marked as paid")
                            results["newly_paid"] += 1
                            results["updated_payments"].append({
                                "billing_id": billing.id,
                                "invoice_number": billing.invoice_number,
                                "job_id": billing.job_id,
                                "customer_name": billing.customer_name,
                                "amount": float(billing.invoice_amount),
                                "payment_details": payment_details
                            })

                            # Update job status to completed
                            job = get_job_by_id(db_session, billing.job_id)
                            if job and job.job_status != JobStatus.COMPLETED:
                                logger.info(f"🔄 Updating job {billing.job_id} status to COMPLETED")
                                updated_job = update_job_status_on_payment(db_session, billing.job_id, JobStatus.COMPLETED)
                                if updated_job:
                                    results["completed_jobs"].append({
                                        "job_id": billing.job_id,
                                        "job_name": job.job_name,
                                        "client_name": job.client_name
                                    })
                                    logger.info(f"🎉 Job {billing.job_id} marked as COMPLETED due to payment")
                                else:
                                    logger.error(f"❌ Failed to update job {billing.job_id} status")
                            else:
                                if job:
                                    logger.info(f"ℹ️  Job {billing.job_id} already completed")
                                else:
                                    logger.error(f"❌ Job {billing.job_id} not found")
                        else:
                            logger.error(f"❌ Failed to mark billing {billing.id} as paid")
                    else:
                        logger.info(f"⏳ Payment still pending for billing {billing.id}")
                        results["still_pending"] += 1

                except Exception as e:
                    results["errors"] += 1
                    error_msg = f"Error checking billing {billing.id}: {str(e)}"
                    results["error_details"].append(error_msg)
                    logger.error(error_msg)

            logger.info(f"Payment check completed: {results['newly_paid']} newly paid, {results['still_pending']} still pending")
            return {
                "success": True,
                "results": results
            }

        except Exception as e:
            logger.error(f"Exception in check_and_update_pending_payments: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": "Exception occurred while checking pending payments"
            }


# Create a global instance
stripe_service = StripeService()
