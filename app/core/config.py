from typing import List, Union
from pydantic import AnyHttpUrl, field_validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    PROJECT_NAME: str = "FastAPI Auth Project"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"

    # Database
    # DATABASE_URL: str = "postgresql://dotec_client_portal_owner:<EMAIL>/dotec_client_portal?sslmode=require"
    DATABASE_URL: str = 'postgresql://dotec_user:dotec200510@localhost/dotec_client_portal'

    # Security
    SECRET_KEY: str = "dotec-hub-production-secret-key-2025-secure-jwt-token-generation"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 4204800

    # CORS - handled directly in main.py for now

    # Environment
    ENVIRONMENT: str = "production"

    # Stripe Configuration
    # STRIPE_PUBLISHABLE_KEY: str = "pk_live_51RdCGLLwSpvX9GtcxfLcjwNk29nGVcLA9oyo57flvb3Qh44c3TuFLQyaATpkeIgx0Q5j1J1bTw7qP72itiqbHAaa00tiyREEL0"  # Replace with actual key
    STRIPE_PUBLISHABLE_KEY: str = "pk_test_51RdCGSPtWzl0hyJlXqGQFJ9VcawUyLUmuwmvb4v6wBml9pxB1HK608LJeNUq0xdWCAwShMA2uEPfvlRGcAcnDuiS00Yf6VrLPq"  # Replace with actual key
    # STRIPE_SECRET_KEY: str = "***********************************************************************************************************"  # Replace with actual key
    STRIPE_SECRET_KEY: str = "sk_test_51RdCGSPtWzl0hyJlKGKsSkhbcznL9mDpoRYPzMzTGLyd0rPm42S5dFgbplxenTsgPdOb4hh1QN5Khni27RYclCaR00x1NNbdxL"  # Replace with actual key
    STRIPE_WEBHOOK_SECRET: str = "whsec_your_stripe_webhook_secret"  # Replace with actual webhook secret
    STRIPE_MODE: str = "live"  # "test" or "live"

    # Base URL for redirects and webhooks
    BASE_URL: str = "http://localhost:8000"

    # Deltek API Configuration
    DELTEK_BASE_URL: str = "https://dotecengineering.deltekfirst.com/DOTecEngineering/api"
    DELTEK_USERNAME: str = "<EMAIL>"
    DELTEK_PASSWORD: str = "Lol@200510"
    DELTEK_CLIENT_ID: str = "c37b290e979e47e582b0f6088bcafde9"
    DELTEK_CLIENT_SECRET: str = "23ad4e75e2d04a0fa0adc42e90d42e76"
    DELTEK_DATABASE: str = "DOTecEngineering"

    # Email Configuration
    MAIL_USERNAME: str = "<EMAIL>"
    MAIL_PASSWORD: str = ""  # Set in environment variables
    MAIL_FROM: str = "<EMAIL>"
    MAIL_PORT: int = 587
    MAIL_SERVER: str = "smtp.gmail.com"  # Change to your SMTP server
    MAIL_FROM_NAME: str = "DOTec Engineering"
    MAIL_STARTTLS: bool = True
    MAIL_SSL_TLS: bool = False
    USE_CREDENTIALS: bool = True
    VALIDATE_CERTS: bool = True

    # Admin notification email
    ADMIN_EMAIL: str = "<EMAIL>"

    model_config = {
        "env_file": ".env",
        "case_sensitive": True,
        "extra":"ignore"
    }

settings = Settings()
