from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Numeric, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.database import Base

class Billing(Base):
    __tablename__ = "billing"
    
    id = Column(Integer, primary_key=True, index=True)
    invoice_number = Column(String, unique=True, index=True, nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    job_id = Column(Integer, ForeignKey("jobs.id"), nullable=False)
    customer_name = Column(String, nullable=False)
    invoice_amount = Column(Numeric(10, 2), nullable=False)
    invoice_date = Column(DateTime(timezone=True), server_default=func.now())
    due_date = Column(DateTime(timezone=True), nullable=True)
    paid = Column(Boolean, default=False, nullable=False)
    # Stripe fields for ACH payments
    stripe_payment_intent_id = Column(String, nullable=True, index=True)  # Stripe Payment Intent ID
    stripe_checkout_session_id = Column(String, nullable=True, index=True)  # Stripe Checkout Session ID
    stripe_customer_id = Column(String, nullable=True, index=True)  # Stripe Customer ID
    payment_method_type = Column(String, nullable=True)  # 'stripe_ach', 'stripe_card', 'stripe_all', etc.
    is_cash_receipt_generated = Column(Boolean, default=False, nullable=False)  # Track if cash receipt was generated in Deltek
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="billings")
    job = relationship("Job", back_populates="billings")
