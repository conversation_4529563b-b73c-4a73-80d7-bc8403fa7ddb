from sqlalchemy import Column, Integer, String, DateTime, Enum, Text, Date, JSON, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.database import Base
import enum

class JobStatus(str, enum.Enum):
    UNDER_REVIEW = "under_review"
    COMPLETED = "completed"
    IN_PROGRESS = "in_progress"
    PENDING_PAYMENT = "pending_payment"

class ServiceType(str, enum.Enum):
    STRUCTURAL = "structural"
    MECHANICAL = "mechanical"
    CIVIL = "civil"
    ELECTRICAL = "electrical"
    FORENSIC = "forensic"
    OTHER = "other"

class Job(Base):
    __tablename__ = "jobs"

    id = Column(Integer, primary_key=True, index=True)
    job_name = Column(String, nullable=False, index=True)
    job_description = Column(Text, nullable=True)
    job_status = Column(Enum(JobStatus, name='jobstatus', values_callable=lambda x: [e.value for e in x]), default=JobStatus.UNDER_REVIEW)
    contract_term = Column(Text, nullable=True)
    contract_amount = Column(String, nullable=True)  # Store as string to handle currency formatting
    payment_id = Column(String, nullable=True, index=True)
    service_type = Column(Enum(ServiceType, name='servicetype', values_callable=lambda x: [e.value for e in x]), nullable=True)
    project_deadline = Column(Date, nullable=True)
    project_documents = Column(JSON, nullable=True)  # Array of document objects with file info
    preferred_contact_number = Column(String, nullable=True)
    client_name = Column(String, nullable=True, index=True)
    client_email = Column(String, nullable=True, index=True)  # Client name for the job

    # Deltek Integration Fields
    deltek_invoice_id = Column(String, nullable=True, index=True)  # Deltek Invoice ID
    deltek_project_number = Column(String, nullable=True, index=True)  # Deltek WBS1
    deltek_billing_client_id = Column(String, nullable=True)  # Deltek BillingClientID
    deltek_original_amount = Column(String, nullable=True)  # Deltek OriginalAmt
    deltek_invoice_date = Column(DateTime(timezone=True), nullable=True)  # Deltek InvoiceDate
    is_deltek_import = Column(Boolean, default=False, nullable=False)  # Flag to identify Deltek imports
    deltek_last_sync = Column(DateTime(timezone=True), nullable=True)  # Last sync timestamp

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    billings = relationship("Billing", back_populates="job")
