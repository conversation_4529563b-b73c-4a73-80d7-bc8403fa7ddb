from sqlalchemy.orm import Session, joinedload
from app.models.billing import Billing
from app.schemas.billing import BillingCreate, BillingUpdate
from typing import List, Optional
import logging

logger = logging.getLogger(__name__)

def create_billing(db: Session, billing: BillingCreate, customer_email: str = None, payment_url: str = None) -> Billing:
    """Create a new billing record and send confirmation email"""
    db_billing = Billing(**billing.dict())
    db.add(db_billing)
    db.commit()
    db.refresh(db_billing)

    # Send billing confirmation email if customer email is provided
    if customer_email:
        try:
            from app.core.email_service import email_service

            logger.info(f"📧 Sending billing confirmation email to {customer_email}")

            email_result = email_service.send_billing_confirmation_email(
                customer_email=customer_email,
                customer_name=billing.customer_name,
                invoice_number=billing.invoice_number,
                amount=float(billing.invoice_amount),
                job_name=f"Job #{billing.job_id}",  # Will be updated with actual job name if available
                payment_url=payment_url
            )

            if email_result["success"]:
                logger.info(f"✅ Billing confirmation email sent successfully to {customer_email}")
            else:
                logger.warning(f"⚠️ Failed to send billing confirmation email: {email_result['message']}")

        except Exception as e:
            logger.error(f"❌ Error sending billing confirmation email: {str(e)}")
            # Don't fail billing creation if email fails

    return db_billing

def get_billing_by_id(db: Session, billing_id: int) -> Optional[Billing]:
    """Get billing record by ID with job details"""
    return db.query(Billing).options(joinedload(Billing.job)).filter(Billing.id == billing_id).first()

def get_billing_by_invoice_number(db: Session, invoice_number: str) -> Optional[Billing]:
    """Get billing record by invoice number with job details"""
    return db.query(Billing).options(joinedload(Billing.job)).filter(Billing.invoice_number == invoice_number).first()

def get_billings_by_job_id(db: Session, job_id: int) -> List[Billing]:
    """Get all billing records for a specific job with job details"""
    return db.query(Billing).options(joinedload(Billing.job)).filter(Billing.job_id == job_id).all()

def get_unpaid_billing_by_job_id(db: Session, job_id: int) -> Optional[Billing]:
    """Get the first unpaid billing record for a specific job"""
    return db.query(Billing).filter(
        Billing.job_id == job_id,
        Billing.paid == False
    ).first()

def get_billings_by_user_id(db: Session, user_id: int, skip: int = 0, limit: int = 100) -> List[Billing]:
    """Get all billing records for a specific user with job details"""
    return db.query(Billing).options(joinedload(Billing.job)).filter(Billing.user_id == user_id).offset(skip).limit(limit).all()

def get_billings(db: Session, skip: int = 0, limit: int = 100) -> List[Billing]:
    """Get all billing records with job details"""
    return db.query(Billing).options(joinedload(Billing.job)).offset(skip).limit(limit).all()

def update_billing(db: Session, billing_id: int, billing_update: BillingUpdate) -> Optional[Billing]:
    """Update a billing record"""
    db_billing = db.query(Billing).filter(Billing.id == billing_id).first()
    if db_billing:
        update_data = billing_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_billing, field, value)
        db.commit()
        db.refresh(db_billing)
    return db_billing

def delete_billing(db: Session, billing_id: int) -> bool:
    """Delete a billing record"""
    db_billing = db.query(Billing).filter(Billing.id == billing_id).first()
    if db_billing:
        db.delete(db_billing)
        db.commit()
        return True
    return False


def get_unpaid_billings_with_stripe(db: Session) -> List[Billing]:
    """Get all unpaid billing records that have Stripe checkout session IDs or payment intent IDs"""
    return db.query(Billing).filter(
        Billing.paid == False,
        (Billing.stripe_checkout_session_id.isnot(None)) | (Billing.stripe_payment_intent_id.isnot(None))
    ).all()

def mark_billing_as_paid(db: Session, billing_id: int) -> Optional[Billing]:
    """Mark a billing record as paid and create cash receipt in Deltek"""
    db_billing = db.query(Billing).filter(Billing.id == billing_id).first()
    if db_billing:
        # Mark billing as paid
        db_billing.paid = True

        # Create cash receipt in Deltek if not already generated and if it's a Deltek job
        if not db_billing.is_cash_receipt_generated:
            try:
                # Get job details for cash receipt
                job = db_billing.job
                if job and job.is_deltek_import and job.deltek_invoice_id and job.deltek_project_number:
                    logger.info(f"💰 Creating cash receipt for billing {billing_id} (Deltek job {job.id})")

                    # Import here to avoid circular imports
                    from app.core.deltek_service import deltek_service

                    # Prepare billing data for cash receipt
                    billing_data = {
                        "job_id": job.id,
                        "job_name": job.job_name,
                        "deltek_project_number": job.deltek_project_number,
                        "deltek_invoice_id": job.deltek_invoice_id,
                        "amount": float(db_billing.invoice_amount)
                    }

                    # Create cash receipt in Deltek
                    cash_receipt_result = deltek_service.create_cash_receipt(billing_data)

                    if cash_receipt_result["success"]:
                        # Mark cash receipt as generated
                        db_billing.is_cash_receipt_generated = True
                        logger.info(f"✅ Cash receipt created successfully for billing {billing_id}")
                    else:
                        logger.error(f"❌ Failed to create cash receipt for billing {billing_id}: {cash_receipt_result.get('message', 'Unknown error')}")
                        # Don't fail the payment marking if cash receipt creation fails

                else:
                    logger.info(f"⏭️ Skipping cash receipt creation for billing {billing_id} - not a Deltek job or missing required data")

            except Exception as e:
                logger.error(f"❌ Error creating cash receipt for billing {billing_id}: {str(e)}")
                # Don't fail the payment marking if cash receipt creation fails

        # Send admin payment notification email
        try:
            from app.core.email_service import email_service

            logger.info(f"📧 Sending payment notification email to admin for billing {billing_id}")

            # Determine payment method
            payment_method = "Unknown"
            if db_billing.payment_method_type:
                if "stripe_ach" in db_billing.payment_method_type:
                    payment_method = "Stripe ACH (Bank Transfer)"
                elif "stripe_card" in db_billing.payment_method_type:
                    payment_method = "Stripe Credit/Debit Card"
                elif "stripe_all" in db_billing.payment_method_type:
                    payment_method = "Stripe (Card/ACH)"
                else:
                    payment_method = db_billing.payment_method_type.replace("stripe_", "Stripe ").title()

            # Get customer email from user if available
            customer_email = "N/A"
            if db_billing.user:
                customer_email = db_billing.user.email

            email_result = email_service.send_payment_notification_email(
                customer_name=db_billing.customer_name,
                invoice_number=db_billing.invoice_number,
                amount=float(db_billing.invoice_amount),
                job_name=job.job_name if job else f"Job #{db_billing.job_id}",
                payment_method=payment_method,
                customer_email=customer_email
            )

            if email_result["success"]:
                logger.info(f"✅ Payment notification email sent successfully to admin")
            else:
                logger.warning(f"⚠️ Failed to send payment notification email: {email_result['message']}")

        except Exception as e:
            logger.error(f"❌ Error sending payment notification email: {str(e)}")
            # Don't fail payment marking if email fails

        db.commit()
        db.refresh(db_billing)
        return db_billing
    return None
