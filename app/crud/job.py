from sqlalchemy.orm import Session, joinedload
from typing import Optional, List
from app.api.v1.endpoints.billing import mark_billing_as_paid
from app.models.job import Job, JobStatus, ServiceType
from app.schemas.job import JobCreate, JobUpdate
from app.core.file_upload import delete_job_documents

def get_job_by_id(db: Session, job_id: int) -> Optional[Job]:
    """
    Get job by ID with billing information
    """
    return db.query(Job).options(joinedload(Job.billings)).filter(Job.id == job_id).first()

def get_jobs(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    client_name: Optional[str] = None,
    job_status: Optional[JobStatus] = None,
    service_type: Optional[ServiceType] = None,
    update_stripe_status: bool = True
) -> List[Job]:
    """
    Get jobs with optional filters and billing information

    Args:
        db: Database session
        skip: Number of records to skip (pagination)
        limit: Maximum number of records to return
        client_name: Filter by client name (optional)
        job_status: Filter by job status (optional)
        service_type: Filter by service type (optional)
        update_stripe_status: Whether to check and update Stripe payment status (default: True)

    Returns:
        List of jobs with updated payment status
    """
    query = db.query(Job).options(joinedload(Job.billings))

    if client_name:
        query = query.filter(Job.client_name.ilike(f"%{client_name}%"))

    if job_status:
        query = query.filter(Job.job_status == job_status)

    if service_type:
        query = query.filter(Job.service_type == service_type)

    jobs = query.offset(skip).limit(limit).all()

    # Update Stripe payment status if requested
    if update_stripe_status:
        from app.core.stripe_service import stripe_service
        from app.crud.billing import mark_billing_as_paid

        print(f"🔍 Admin: Checking Stripe payment status for {len(jobs)} jobs...")

        for job in jobs:
            job_payment_updated = False
            job_has_paid_stripe_billings = False

            for billing in job.billings:
                # Check unpaid billings that have Stripe checkout session IDs or payment intent IDs
                if not billing.paid and (billing.stripe_checkout_session_id or billing.stripe_payment_intent_id):
                    try:
                        payment_status = None

                        # Check checkout session first (preferred method)
                        if billing.stripe_checkout_session_id:
                            payment_status = stripe_service.get_checkout_session_status(billing.stripe_checkout_session_id)
                            stripe_id = billing.stripe_checkout_session_id
                            stripe_type = "checkout session"
                        # Fallback to payment intent if no checkout session
                        elif billing.stripe_payment_intent_id:
                            payment_status = stripe_service.get_payment_intent_status(billing.stripe_payment_intent_id)
                            stripe_id = billing.stripe_payment_intent_id
                            stripe_type = "payment intent"

                        if payment_status and payment_status["success"] and payment_status["is_paid"]:
                            # Mark billing as paid in database
                            mark_billing_as_paid(db, billing.id)
                            billing.paid = True  # Update the object in memory too
                            job_payment_updated = True
                            job_has_paid_stripe_billings = True
                            print(f"✅ Admin: Updated billing {billing.id} as paid (Stripe {stripe_type}: {stripe_id})")

                    except Exception as e:
                        print(f"⚠️  Admin: Error checking Stripe status for billing {billing.id}: {str(e)}")
                        continue
                elif billing.paid and (billing.stripe_checkout_session_id or billing.stripe_payment_intent_id):
                    # This billing is already paid - consider it for job completion
                    job_has_paid_stripe_billings = True

            # Update job status to completed if there are paid Stripe billings and job isn't already completed
            if (job_payment_updated or job_has_paid_stripe_billings) and job.job_status != JobStatus.COMPLETED:
                # Debug: Show billing status for this job
                print(f"🔍 Admin: Checking job {job.id} for completion:")
                stripe_billings = [b for b in job.billings if (b.stripe_checkout_session_id or b.stripe_payment_intent_id)]
                all_billings = job.billings

                print(f"   Total billings: {len(all_billings)}")
                print(f"   Stripe billings: {len(stripe_billings)}")

                for billing in all_billings:
                    status = "PAID" if billing.paid else "UNPAID"
                    if billing.stripe_checkout_session_id:
                        stripe_info = f" (Stripe session: {billing.stripe_checkout_session_id})"
                    elif billing.stripe_payment_intent_id:
                        stripe_info = f" (Stripe intent: {billing.stripe_payment_intent_id})"
                    else:
                        stripe_info = " (No Stripe)"
                    print(f"   Billing {billing.id}: {status}{stripe_info}")

                # Check if all Stripe billings are paid (only consider billings with Stripe IDs)
                if stripe_billings:  # Only proceed if there are Stripe billings
                    all_stripe_billings_paid = all(billing.paid for billing in stripe_billings)

                    print(f"   All Stripe billings paid: {all_stripe_billings_paid}")

                    if all_stripe_billings_paid:
                        # Update job status to completed
                        job.job_status = JobStatus.COMPLETED
                        db.commit()
                        print(f"🎉 Admin: Job {job.id} marked as COMPLETED due to Stripe payment completion")
                    else:
                        print(f"   Job {job.id} not completed - some Stripe billings still unpaid")
                else:
                    print(f"   Job {job.id} has no Stripe billings - skipping completion check")

    return jobs

def get_jobs_count(
    db: Session,
    client_name: Optional[str] = None,
    job_status: Optional[JobStatus] = None,
    service_type: Optional[ServiceType] = None
) -> int:
    """
    Get total count of jobs with optional filters
    """
    query = db.query(Job)

    if client_name:
        query = query.filter(Job.client_name.ilike(f"%{client_name}%"))

    if job_status:
        query = query.filter(Job.job_status == job_status)

    if service_type:
        query = query.filter(Job.service_type == service_type)

    return query.count()

def create_job(db: Session, job: JobCreate) -> Job:
    """
    Create new job
    """
    db_job = Job(
        job_name=job.job_name,
        job_description=job.job_description,
        job_status=job.job_status,  # Include job_status from the JobCreate schema
        contract_term=job.contract_term,
        contract_amount=job.contract_amount,
        payment_id=job.payment_id,
        service_type=job.service_type,
        project_deadline=job.project_deadline,
        project_documents=None,  # Documents are uploaded separately
        preferred_contact_number=job.preferred_contact_number,
        client_name=job.client_name,
        client_email=job.client_email
    )
    db.add(db_job)
    db.commit()
    db.refresh(db_job)
    return db_job

def update_job(db: Session, job_id: int, job_update: JobUpdate) -> Optional[Job]:
    """
    Update job
    """
    job = get_job_by_id(db, job_id)
    if not job:
        return None

    update_data = job_update.model_dump(exclude_unset=True)

    for field, value in update_data.items():
        setattr(job, field, value)

    db.commit()
    db.refresh(job)
    return job

def delete_job(db: Session, job_id: int) -> bool:
    """
    Delete job
    """
    job = get_job_by_id(db, job_id)
    if not job:
        return False

    # Delete associated documents from storage
    if job.project_documents:
        delete_job_documents(job.project_documents)

    db.delete(job)
    db.commit()
    return True

def update_job_status_on_payment(db: Session, job_id: int, new_status: JobStatus) -> Optional[Job]:
    """
    Update job status when payment is received

    Args:
        db: Database session
        job_id: ID of the job to update
        new_status: New job status (typically COMPLETED)

    Returns:
        Updated job object or None if not found
    """
    job = db.query(Job).filter(Job.id == job_id).first()
    if job:
        old_status = job.job_status
        job.job_status = new_status
        db.commit()
        db.refresh(job)
        print(f"🔄 Updated job {job_id} status: {old_status} → {new_status}")
        return job
    return None

def get_jobs_by_client(db: Session, client_name: str, skip: int = 0, limit: int = 100, update_stripe_status: bool = True) -> List[Job]:
    """
    Get all jobs for a specific client with billing information
    Optionally updates Stripe payment status and job status for unpaid invoices
    """
    from app.core.stripe_service import stripe_service
    from app.models.job import JobStatus

    # Get jobs with billing information
    jobs = db.query(Job).options(joinedload(Job.billings)).filter(Job.client_name.ilike(f"%{client_name}%")).offset(skip).limit(limit).all()

    # Update Stripe payment status if requested
    if update_stripe_status:
        for job in jobs:
            job_payment_updated = False
            job_has_paid_stripe_billings = False

            for billing in job.billings:
                # Check unpaid billings that have Stripe checkout session IDs or payment intent IDs
                if not billing.paid and (billing.stripe_checkout_session_id or billing.stripe_checkout_session_id):
                    try:
                        payment_status = None

                        # Check checkout session first (preferred method)
                        if billing.stripe_checkout_session_id:
                            payment_status = stripe_service.get_checkout_session_status(billing.stripe_checkout_session_id)
                            stripe_id = billing.stripe_checkout_session_id
                            stripe_type = "checkout session"
                        # Fallback to payment intent if no checkout session
                        elif billing.stripe_payment_intent_id:
                            payment_status = stripe_service.get_payment_intent_status(billing.stripe_payment_intent_id)
                            stripe_id = billing.stripe_payment_intent_id
                            stripe_type = "payment intent"

                        if payment_status and payment_status["success"] and payment_status["is_paid"]:
                            # Mark billing as paid in database
                            mark_billing_as_paid(db, billing.id)
                            billing.paid = True  # Update the object in memory too
                            job_payment_updated = True
                            job_has_paid_stripe_billings = True
                            print(f"✅ Updated billing {billing.id} as paid (Stripe {stripe_type}: {stripe_id})")

                    except Exception as e:
                        print(f"⚠️  Error checking Stripe status for billing {billing.id}: {str(e)}")
                        continue
                elif billing.paid and (billing.stripe_checkout_session_id or billing.stripe_payment_intent_id):
                    # This billing is already paid - consider it for job completion
                    job_has_paid_stripe_billings = True

            # Update job status to completed if there are paid Stripe billings and job isn't already completed
            # This includes both newly paid billings and already paid billings
            if (job_payment_updated or job_has_paid_stripe_billings) and job.job_status != JobStatus.COMPLETED:
                # Debug: Show billing status for this job
                print(f"🔍 Checking job {job.id} for completion:")
                stripe_billings = [b for b in job.billings if (b.stripe_checkout_session_id or b.stripe_payment_intent_id)]
                all_billings = job.billings

                print(f"   Total billings: {len(all_billings)}")
                print(f"   Stripe billings: {len(stripe_billings)}")

                for billing in all_billings:
                    status = "PAID" if billing.paid else "UNPAID"
                    if billing.stripe_checkout_session_id:
                        stripe_info = f" (Stripe session: {billing.stripe_checkout_session_id})"
                    elif billing.stripe_payment_intent_id:
                        stripe_info = f" (Stripe intent: {billing.stripe_payment_intent_id})"
                    else:
                        stripe_info = " (No Stripe)"
                    print(f"   Billing {billing.id}: {status}{stripe_info}")

                # Check if all Stripe billings are paid (only consider billings with Stripe IDs)
                if stripe_billings:  # Only proceed if there are Stripe billings
                    all_stripe_billings_paid = all(billing.paid for billing in stripe_billings)
                    print(f"   All Stripe billings paid: {all_stripe_billings_paid}")

                    if all_stripe_billings_paid:
                        print(f"   🎯 Attempting to mark job {job.id} as COMPLETED...")
                        updated_job = update_job_status_on_payment(db, job.id, JobStatus.COMPLETED)
                        if updated_job:
                            job.job_status = JobStatus.COMPLETED  # Update the object in memory too
                            print(f"🎉 Job {job.id} marked as COMPLETED due to payment received")
                        else:
                            print(f"❌ Failed to update job {job.id} status to COMPLETED")
                    else:
                        print(f"   ⏳ Job {job.id} not completed - some Stripe billings still unpaid")
                else:
                    print(f"   ℹ️  Job {job.id} has no Stripe billings - skipping auto-completion")

    return jobs
