# Core FastAPI dependencies
fastapi==0.104.1
uvicorn==0.24.0

# Database
sqlalchemy==2.0.23
psycopg2-binary==2.9.9

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Configuration
pydantic[email]==2.5.0
pydantic-settings==2.1.0
python-dotenv==1.0.0

# Stripe Integration
stripe==12.2.0
requests==2.31.0

# File handling
aiofiles==23.2.1

# Date/Time utilities
python-dateutil==2.8.2

# Background job scheduling
APScheduler==3.10.4

# Email services
fastapi-mail==1.4.1
jinja2==3.1.2
