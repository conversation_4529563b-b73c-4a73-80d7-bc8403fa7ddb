// Frontend Integration Example for Webhook-Free Payment System
// This shows how to integrate the payment status checking in your frontend

class PaymentStatusChecker {
    constructor(apiBaseUrl) {
        this.apiBaseUrl = apiBaseUrl;
        this.isChecking = false;
    }

    /**
     * Check all pending payments and update UI
     * Call this on page load or periodically
     */
    async checkAllPendingPayments() {
        if (this.isChecking) {
            console.log('Payment check already in progress...');
            return;
        }

        this.isChecking = true;
        
        try {
            console.log('🔍 Checking for payment updates...');
            
            const response = await fetch(`${this.apiBaseUrl}/api/v1/jobs/check-all-pending-payments`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });

            const result = await response.json();

            if (result.success) {
                console.log(`✅ Payment check completed: ${result.summary.newly_paid} newly paid`);
                
                // Show notifications for newly paid invoices
                if (result.updated_payments.length > 0) {
                    this.showPaymentNotifications(result.updated_payments);
                }

                // Show notifications for completed jobs
                if (result.completed_jobs.length > 0) {
                    this.showJobCompletionNotifications(result.completed_jobs);
                }

                // Refresh the jobs list to show updated status
                this.refreshJobsList();

                return result;
            } else {
                console.error('❌ Payment check failed:', result.message);
                return null;
            }
        } catch (error) {
            console.error('❌ Error checking payments:', error);
            return null;
        } finally {
            this.isChecking = false;
        }
    }

    /**
     * Check payment status for a specific job
     */
    async checkJobPaymentStatus(jobId) {
        try {
            const response = await fetch(`${this.apiBaseUrl}/api/v1/jobs/${jobId}/check-payment-status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });

            const result = await response.json();
            
            if (result.success) {
                console.log(`✅ Job ${jobId} payment check completed`);
                return result.results;
            } else {
                console.error(`❌ Job ${jobId} payment check failed:`, result.message);
                return null;
            }
        } catch (error) {
            console.error(`❌ Error checking job ${jobId} payment:`, error);
            return null;
        }
    }

    /**
     * Show notifications for newly paid invoices
     */
    showPaymentNotifications(updatedPayments) {
        updatedPayments.forEach(payment => {
            this.showNotification(
                'Payment Received! 💰',
                `Payment of $${payment.amount} received for ${payment.customer_name}`,
                'success'
            );
        });
    }

    /**
     * Show notifications for completed jobs
     */
    showJobCompletionNotifications(completedJobs) {
        completedJobs.forEach(job => {
            this.showNotification(
                'Job Completed! 🎉',
                `Job "${job.job_name}" for ${job.client_name} has been marked as completed`,
                'success'
            );
        });
    }

    /**
     * Show a notification to the user
     */
    showNotification(title, message, type = 'info') {
        // Replace this with your notification system
        console.log(`${type.toUpperCase()}: ${title} - ${message}`);
        
        // Example with browser notification
        if (Notification.permission === 'granted') {
            new Notification(title, { body: message });
        }
        
        // Example with toast notification (if using a toast library)
        // toast.success(`${title}: ${message}`);
    }

    /**
     * Refresh the jobs list to show updated status
     */
    async refreshJobsList() {
        // Replace this with your jobs list refresh logic
        console.log('🔄 Refreshing jobs list...');
        
        // Example: Trigger a re-fetch of jobs
        if (window.jobsComponent && window.jobsComponent.refresh) {
            window.jobsComponent.refresh();
        }
    }

    /**
     * Get authentication token (replace with your auth system)
     */
    getAuthToken() {
        // Replace this with your authentication token retrieval
        return localStorage.getItem('authToken') || '';
    }

    /**
     * Start periodic payment checking
     */
    startPeriodicChecking(intervalMinutes = 5) {
        console.log(`🕐 Starting periodic payment checking every ${intervalMinutes} minutes`);
        
        // Check immediately
        this.checkAllPendingPayments();
        
        // Then check periodically
        setInterval(() => {
            this.checkAllPendingPayments();
        }, intervalMinutes * 60 * 1000);
    }

    /**
     * Stop periodic checking
     */
    stopPeriodicChecking() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
            console.log('⏹️ Stopped periodic payment checking');
        }
    }
}

// Usage Examples:

// 1. Initialize the payment checker
const paymentChecker = new PaymentStatusChecker('https://dotec-hub.vercel.app');

// 2. Check payments when page loads
document.addEventListener('DOMContentLoaded', async () => {
    await paymentChecker.checkAllPendingPayments();
});

// 3. Start periodic checking (every 5 minutes)
paymentChecker.startPeriodicChecking(5);

// 4. Check payments when user clicks refresh button
document.getElementById('refreshButton')?.addEventListener('click', () => {
    paymentChecker.checkAllPendingPayments();
});

// 5. Check specific job payment when viewing job details
async function loadJobDetails(jobId) {
    // Check payment status first
    await paymentChecker.checkJobPaymentStatus(jobId);
    
    // Then load job details
    const jobDetails = await fetch(`/api/v1/jobs/${jobId}`);
    return jobDetails.json();
}

// 6. Integration with React/Vue components
class JobsComponent {
    async componentDidMount() {
        // Check payments before loading jobs
        await paymentChecker.checkAllPendingPayments();
        this.loadJobs();
    }

    async handleRefresh() {
        this.setState({ loading: true });
        await paymentChecker.checkAllPendingPayments();
        await this.loadJobs();
        this.setState({ loading: false });
    }
}

// 7. Integration with job creation flow
async function afterPaymentCreated(jobId, checkoutUrl) {
    // Redirect user to Stripe checkout
    window.location.href = checkoutUrl;
    
    // When they return, check payment status
    // (This would be on your success/return page)
    setTimeout(async () => {
        await paymentChecker.checkJobPaymentStatus(jobId);
    }, 2000); // Wait 2 seconds for Stripe to process
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PaymentStatusChecker;
}
