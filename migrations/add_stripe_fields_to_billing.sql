-- Migration: Add Stripe fields to billing table
-- Date: 2025-01-25
-- Description: Add support for Stripe ACH payments by adding Stripe-specific fields

-- Add Stripe payment intent ID column
ALTER TABLE billing 
ADD COLUMN stripe_payment_intent_id VARCHAR(255) NULL;

-- Add Stripe customer ID column  
ALTER TABLE billing 
ADD COLUMN stripe_customer_id VARCHAR(255) NULL;

-- Add payment method type column to distinguish between PayPal and Stripe
ALTER TABLE billing 
ADD COLUMN payment_method_type VARCHAR(50) NULL;

-- Create indexes for better query performance
CREATE INDEX idx_billing_stripe_payment_intent_id ON billing(stripe_payment_intent_id);
CREATE INDEX idx_billing_stripe_customer_id ON billing(stripe_customer_id);
CREATE INDEX idx_billing_payment_method_type ON billing(payment_method_type);

-- Update existing records to set payment_method_type
UPDATE billing 
SET payment_method_type = 'paypal' 
WHERE paypal_invoice_id IS NOT NULL AND payment_method_type IS NULL;

-- Add comments to document the new columns
COMMENT ON COLUMN billing.stripe_payment_intent_id IS 'Stripe Payment Intent ID for ACH payments';
COMMENT ON COLUMN billing.stripe_customer_id IS 'Stripe Customer ID for payment processing';
COMMENT ON COLUMN billing.payment_method_type IS 'Payment method type: paypal, stripe_ach, etc.';
