-- Migration: Remove PayPal fields from billing table
-- Date: 2025-01-25
-- Description: Remove PayPal-specific fields as we're switching to Stripe-only payments

-- Drop PayPal-related index first
DROP INDEX IF EXISTS idx_billing_paypal_invoice_id;

-- Remove PayPal invoice ID column
ALTER TABLE billing 
DROP COLUMN IF EXISTS paypal_invoice_id;

-- Update payment_method_type for any records that had 'paypal' to NULL or 'stripe_ach'
-- This is optional - you might want to keep historical data
UPDATE billing 
SET payment_method_type = NULL 
WHERE payment_method_type = 'paypal';

-- Add comment to document the change
COMMENT ON TABLE billing IS 'Billing records - PayPal support removed, Stripe ACH only';
