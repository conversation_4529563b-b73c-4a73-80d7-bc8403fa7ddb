-- Migration: Add Stripe checkout session ID to billing table
-- Date: 2025-01-25
-- Description: Add stripe_checkout_session_id column to track Stripe checkout sessions for payment status checking

-- Add the new column
ALTER TABLE billing 
ADD COLUMN stripe_checkout_session_id VARCHAR(255);

-- Add index for faster lookups
CREATE INDEX idx_billing_stripe_checkout_session_id 
ON billing(stripe_checkout_session_id);

-- Add comment to document the change
COMMENT ON COLUMN billing.stripe_checkout_session_id IS 'Stripe checkout session ID for payment status tracking without webhooks';
