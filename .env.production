# Production Environment Configuration
# DOTec Hub - Client Portal

# Environment
ENVIRONMENT=production

# Database Configuration
# DATABASE_URL=postgresql://dotec_client_portal_owner:<EMAIL>/dotec_client_portal?sslmode=require
DATABASE_URL=postgresql://dotec_user:dotec200510@localhost/dotec_client_portal

# Security Configuration
SECRET_KEY=dotec-hub-production-secret-key-2025-secure-jwt-token-generation
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=4204800

# Application Configuration
PROJECT_NAME=DOTec Hub - Client Portal
VERSION=1.0.0
API_V1_STR=/api/v1

# Base URL Configuration
BASE_URL=https://pay.dotecengineering.com

# Stripe Configuration (Test/Production)
# STRIPE_PUBLISHABLE_KEY=pk_test_51RdCGSPtWzl0hyJlXqGQFJ9VcawUyLUmuwmvb4v6wBml9pxB1HK608LJeNUq0xdWCAwShMA2uEPfvlRGcAcnDuiS00Yf6VrLPq
STRIPE_PUBLISHABLE_KEY=pk_live_51RdCGLLwSpvX9GtcxfLcjwNk29nGVcLA9oyo57flvb3Qh44c3TuFLQyaATpkeIgx0Q5j1J1bTw7qP72itiqbHAaa00tiyREEL0
STRIPE_SECRET_KEY=***********************************************************************************************************
# STRIPE_SECRET_KEY=sk_test_51RdCGSPtWzl0hyJlKGKsSkhbcznL9mDpoRYPzMzTGLyd0rPm42S5dFgbplxenTsgPdOb4hh1QN5Khni27RYclCaR00x1NNbdxL
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret
STRIPE_MODE=live

# CORS Configuration (if needed)
BACKEND_CORS_ORIGINS=["https://pay.dotecengineering.com", "https://dotecengineering.com"]

# Logging Configuration
LOG_LEVEL=INFO

# File Upload Configuration
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_FILE_TYPES=["pdf", "doc", "docx", "xls", "xlsx", "jpg", "jpeg", "png", "zip"]

# Email Configuration (if needed)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# Redis Configuration (if using caching)
# REDIS_URL=redis://localhost:6379/0

# Monitoring and Analytics
SENTRY_DSN=your-sentry-dsn-for-error-tracking
ANALYTICS_ID=your-analytics-id
