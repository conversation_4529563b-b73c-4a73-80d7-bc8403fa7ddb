# FastAPI Authentication & Authorization Project

A complete FastAPI project with PostgreSQL database, JWT authentication, and role-based authorization.

## Features

- **FastAPI** framework with automatic API documentation
- **PostgreSQL** database with SQLAlchemy ORM
- **JWT Authentication** with secure password hashing
- **Role-based Authorization** (Admin, Moderator, User)
- **Database Migrations** with Alembic
- **Docker** support for easy deployment
- **Comprehensive Testing** with pytest
- **CORS** support for frontend integration

## Project Structure

```
├── app/
│   ├── api/v1/
│   │   ├── endpoints/
│   │   │   ├── auth.py          # Authentication endpoints
│   │   │   └── users.py         # User management endpoints
│   │   └── api.py               # API router
│   ├── core/
│   │   ├── config.py            # Configuration settings
│   │   ├── deps.py              # Dependencies and authorization
│   │   └── security.py          # JWT and password utilities
│   ├── crud/
│   │   └── user.py              # Database operations
│   ├── db/
│   │   └── database.py          # Database connection
│   ├── models/
│   │   └── user.py              # SQLAlchemy models
│   ├── schemas/
│   │   └── user.py              # Pydantic schemas
│   └── main.py                  # FastAPI application
├── tests/                       # Test files
├── alembic/                     # Database migrations
├── requirements.txt             # Python dependencies
├── docker-compose.yml           # Docker services
└── .env                         # Environment variables
```

## Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Set up Environment Variables

Copy `.env` file and update the database URL and secret key:

```bash
# Database
DATABASE_URL=postgresql://username:password@localhost:5432/fastapi_db

# Security
SECRET_KEY=your-secret-key-here-change-this-in-production
```

### 3. Set up Database

Start PostgreSQL (or use Docker):

```bash
docker-compose up -d db
```

Run database migrations:

```bash
alembic upgrade head
```

### 4. Run the Application

```bash
uvicorn app.main:app --reload
```

The API will be available at `http://localhost:8000`

### 5. Access API Documentation

- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`

## Docker Deployment

Run the entire stack with Docker:

```bash
docker-compose up
```

## API Endpoints

### Authentication
- `POST /api/v1/auth/register` - Register new user
- `POST /api/v1/auth/login` - Login user
- `POST /api/v1/auth/token` - OAuth2 compatible token endpoint

### Users
- `GET /api/v1/users/me` - Get current user profile
- `PUT /api/v1/users/me` - Update current user profile
- `GET /api/v1/users/profile` - Get user profile (protected)
- `GET /api/v1/users/admin-only` - Admin only endpoint
- `GET /api/v1/users/moderator-or-admin` - Moderator/Admin endpoint
- `GET /api/v1/users/{user_id}` - Get user by ID (moderator/admin)
- `PUT /api/v1/users/{user_id}` - Update user by ID (admin only)

## User Roles

- **USER**: Default role, can access basic protected endpoints
- **MODERATOR**: Can view other users, moderate content
- **ADMIN**: Full access to all endpoints and user management

## Testing

Run tests:

```bash
pytest
```

Run tests with coverage:

```bash
pytest --cov=app
```

## Example Usage

### 1. Register a new user

```bash
curl -X POST "http://localhost:8000/api/v1/auth/register" \
     -H "Content-Type: application/json" \
     -d '{
       "email": "<EMAIL>",
       "username": "testuser",
       "password": "securepassword123",
       "full_name": "Test User"
     }'
```

### 2. Login and get token

```bash
curl -X POST "http://localhost:8000/api/v1/auth/login" \
     -H "Content-Type: application/json" \
     -d '{
       "email": "<EMAIL>",
       "password": "securepassword123"
     }'
```

### 3. Access protected endpoint

```bash
curl -X GET "http://localhost:8000/api/v1/users/me" \
     -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## Security Features

- **Password Hashing**: Uses bcrypt for secure password storage
- **JWT Tokens**: Stateless authentication with configurable expiration
- **Role-based Access Control**: Fine-grained permissions system
- **CORS Protection**: Configurable cross-origin resource sharing
- **Input Validation**: Pydantic schemas for request/response validation

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DATABASE_URL` | PostgreSQL connection string | Required |
| `SECRET_KEY` | JWT signing secret | Required |
| `ACCESS_TOKEN_EXPIRE_MINUTES` | Token expiration time | 30 |
| `BACKEND_CORS_ORIGINS` | Allowed CORS origins | [] |

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run tests and ensure they pass
6. Submit a pull request
