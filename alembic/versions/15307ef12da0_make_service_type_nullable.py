"""make_service_type_nullable

Revision ID: 15307ef12da0
Revises: 393e7492f11a
Create Date: 2025-06-19 19:22:05.665288

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '15307ef12da0'
down_revision: Union[str, None] = '393e7492f11a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Make service_type column nullable
    op.alter_column('jobs', 'service_type', nullable=True)


def downgrade() -> None:
    # Make service_type column non-nullable (revert the change)
    op.alter_column('jobs', 'service_type', nullable=False)
