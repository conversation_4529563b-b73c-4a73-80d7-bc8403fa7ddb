"""clean_duplicate_enum_values

Revision ID: 36bb7eb220b9
Revises: 7ff02fc0cdae
Create Date: 2025-06-19 20:30:48.628637

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '36bb7eb220b9'
down_revision: Union[str, None] = '7ff02fc0cdae'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Clean up duplicate enum values by recreating the enum type with correct values
    # First, create a new enum type with the correct values
    op.execute("CREATE TYPE jobstatus_new AS ENUM ('under_review', 'completed', 'in_progress', 'pending_payment')")

    # Update the column to use the new enum type
    op.execute("ALTER TABLE jobs ALTER COLUMN job_status TYPE jobstatus_new USING job_status::text::jobstatus_new")

    # Drop the old enum type and rename the new one
    op.execute("DROP TYPE jobstatus")
    op.execute("ALTER TYPE jobstatus_new RENAME TO jobstatus")


def downgrade() -> None:
    # Recreate the old enum type with duplicates (not recommended, but for completeness)
    op.execute("CREATE TYPE jobstatus_old AS ENUM ('UNDER_REVIEW', 'COMPLETED', 'IN_PROGRESS', 'PENDING_PAYMENT', 'under_review', 'completed', 'in_progress', 'pending_payment')")
    op.execute("ALTER TABLE jobs ALTER COLUMN job_status TYPE jobstatus_old USING job_status::text::jobstatus_old")
    op.execute("DROP TYPE jobstatus")
    op.execute("ALTER TYPE jobstatus_old RENAME TO jobstatus")
