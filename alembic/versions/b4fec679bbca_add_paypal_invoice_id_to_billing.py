"""add_paypal_invoice_id_to_billing

Revision ID: b4fec679bbca
Revises: 4914061a47ec
Create Date: 2025-06-21 06:34:41.818556

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b4fec679bbca'
down_revision: Union[str, None] = '4914061a47ec'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
