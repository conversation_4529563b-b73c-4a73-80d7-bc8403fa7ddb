"""add_pending_payment_to_jobstatus_enum

Revision ID: 7ff02fc0cdae
Revises: 15307ef12da0
Create Date: 2025-06-19 20:26:28.630610

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '7ff02fc0cdae'
down_revision: Union[str, None] = '15307ef12da0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add 'pending_payment' to the jobstatus enum
    op.execute("ALTER TYPE jobstatus ADD VALUE 'pending_payment'")


def downgrade() -> None:
    # Note: PostgreSQL doesn't support removing enum values directly
    # This would require recreating the enum type and updating all references
    # For safety, we'll leave this as a no-op
    pass
