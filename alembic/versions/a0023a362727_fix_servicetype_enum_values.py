"""fix_servicetype_enum_values

Revision ID: a0023a362727
Revises: 36bb7eb220b9
Create Date: 2025-06-19 20:32:29.787883

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a0023a362727'
down_revision: Union[str, None] = '36bb7eb220b9'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Fix ServiceType enum values to match Python enum (lowercase)
    # Create a new enum type with the correct values
    op.execute("CREATE TYPE servicetype_new AS ENUM ('structural', 'mechanical', 'civil', 'electrical', 'forensic', 'other')")

    # Update the column to use the new enum type, converting uppercase to lowercase
    op.execute("""
        ALTER TABLE jobs
        ALTER COLUMN service_type TYPE servicetype_new
        USING CASE
            WHEN service_type IS NULL THEN NULL
            ELSE LOWER(service_type::text)::servicetype_new
        END
    """)

    # Drop the old enum type and rename the new one
    op.execute("DROP TYPE servicetype")
    op.execute("ALTER TYPE servicetype_new RENAME TO servicetype")


def downgrade() -> None:
    # Recreate the old enum type with uppercase values
    op.execute("CREATE TYPE servicetype_old AS ENUM ('STRUCTURAL', 'MECHANICAL', 'CIVIL', 'ELECTRICAL', 'FORENSIC', 'OTHER')")
    op.execute("""
        ALTER TABLE jobs
        ALTER COLUMN service_type TYPE servicetype_old
        USING CASE
            WHEN service_type IS NULL THEN NULL
            ELSE UPPER(service_type::text)::servicetype_old
        END
    """)
    op.execute("DROP TYPE servicetype")
    op.execute("ALTER TYPE servicetype_old RENAME TO servicetype")
