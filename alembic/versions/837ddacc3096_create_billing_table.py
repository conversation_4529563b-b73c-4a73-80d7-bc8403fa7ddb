"""create_billing_table

Revision ID: 837ddacc3096
Revises: a0023a362727
Create Date: 2025-06-20 02:35:23.120135

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '837ddacc3096'
down_revision: Union[str, None] = 'a0023a362727'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create billing table
    op.create_table('billing',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('invoice_number', sa.String(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('job_id', sa.Integer(), nullable=False),
        sa.Column('customer_name', sa.String(), nullable=False),
        sa.Column('invoice_amount', sa.Numeric(precision=10, scale=2), nullable=False),
        sa.Column('invoice_date', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('due_date', sa.DateTime(timezone=True), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['job_id'], ['jobs.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_billing_id'), 'billing', ['id'], unique=False)
    op.create_index(op.f('ix_billing_invoice_number'), 'billing', ['invoice_number'], unique=True)


def downgrade() -> None:
    # Drop billing table
    op.drop_index(op.f('ix_billing_invoice_number'), table_name='billing')
    op.drop_index(op.f('ix_billing_id'), table_name='billing')
    op.drop_table('billing')
