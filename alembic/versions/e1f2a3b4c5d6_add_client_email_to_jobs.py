"""Add client_email to jobs table

Revision ID: e1f2a3b4c5d6
Revises: d2d9e403a0e7
Create Date: 2025-06-29 12:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e1f2a3b4c5d6'
down_revision: Union[str, None] = 'd2d9e403a0e7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('jobs', sa.Column('client_email', sa.String(), nullable=True))
    op.create_index(op.f('ix_jobs_client_email'), 'jobs', ['client_email'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_jobs_client_email'), table_name='jobs')
    op.drop_column('jobs', 'client_email')
    # ### end Alembic commands ###
