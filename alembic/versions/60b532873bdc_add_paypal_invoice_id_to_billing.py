"""add_paypal_invoice_id_to_billing

Revision ID: 60b532873bdc
Revises: b4fec679bbca
Create Date: 2025-06-21 08:27:03.112237

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '60b532873bdc'
down_revision: Union[str, None] = 'b4fec679bbca'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('billing', sa.Column('paypal_invoice_id', sa.String(), nullable=True))
    op.create_index(op.f('ix_billing_paypal_invoice_id'), 'billing', ['paypal_invoice_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_billing_paypal_invoice_id'), table_name='billing')
    op.drop_column('billing', 'paypal_invoice_id')
    # ### end Alembic commands ###
