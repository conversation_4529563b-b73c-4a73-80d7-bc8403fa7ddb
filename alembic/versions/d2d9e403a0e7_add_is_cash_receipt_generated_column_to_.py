"""Add is_cash_receipt_generated column to billing table

Revision ID: d2d9e403a0e7
Revises: 4094fd271f64
Create Date: 2025-06-26 13:36:11.972793

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd2d9e403a0e7'
down_revision: Union[str, None] = '4094fd271f64'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('billing', sa.Column('is_cash_receipt_generated', sa.<PERSON>(), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('billing', 'is_cash_receipt_generated')
    # ### end Alembic commands ###
