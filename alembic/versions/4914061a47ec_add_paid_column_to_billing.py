"""add_paid_column_to_billing

Revision ID: 4914061a47ec
Revises: 837ddacc3096
Create Date: 2025-06-20 02:53:21.046240

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4914061a47ec'
down_revision: Union[str, None] = '837ddacc3096'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add paid column to billing table
    op.add_column('billing', sa.Column('paid', sa.<PERSON>(), nullable=False, server_default='false'))


def downgrade() -> None:
    # Remove paid column from billing table
    op.drop_column('billing', 'paid')
