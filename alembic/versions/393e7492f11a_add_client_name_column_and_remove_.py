"""Add client_name column and remove created_by from jobs

Revision ID: 393e7492f11a
Revises: 
Create Date: 2025-06-19 19:11:52.323411

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '393e7492f11a'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('jobs', sa.Column('client_name', sa.String(), nullable=True))
    op.create_index(op.f('ix_jobs_client_name'), 'jobs', ['client_name'], unique=False)
    op.drop_constraint('jobs_created_by_fkey', 'jobs', type_='foreignkey')
    op.drop_column('jobs', 'created_by')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('jobs', sa.Column('created_by', sa.INTEGER(), autoincrement=False, nullable=False))
    op.create_foreign_key('jobs_created_by_fkey', 'jobs', 'users', ['created_by'], ['id'])
    op.drop_index(op.f('ix_jobs_client_name'), table_name='jobs')
    op.drop_column('jobs', 'client_name')
    # ### end Alembic commands ###
