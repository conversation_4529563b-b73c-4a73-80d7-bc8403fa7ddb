"""Add Deltek integration fields to jobs table

Revision ID: 4094fd271f64
Revises: 60b532873bdc
Create Date: 2025-06-26 09:03:33.212760

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4094fd271f64'
down_revision: Union[str, None] = '60b532873bdc'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('billing', 'stripe_payment_intent_id',
               existing_type=sa.VARCHAR(length=255),
               comment=None,
               existing_comment='Stripe Payment Intent ID for ACH payments',
               existing_nullable=True)
    op.alter_column('billing', 'stripe_customer_id',
               existing_type=sa.VARCHAR(length=255),
               comment=None,
               existing_comment='Stripe Customer ID for payment processing',
               existing_nullable=True)
    op.alter_column('billing', 'payment_method_type',
               existing_type=sa.VARCHAR(length=50),
               comment=None,
               existing_comment='Payment method type: paypal, stripe_ach, etc.',
               existing_nullable=True)
    op.drop_index('idx_billing_payment_method_type', table_name='billing')
    op.drop_index('idx_billing_stripe_checkout_session_id', table_name='billing')
    op.drop_index('idx_billing_stripe_customer_id', table_name='billing')
    op.drop_index('idx_billing_stripe_payment_intent_id', table_name='billing')
    op.create_index(op.f('ix_billing_stripe_checkout_session_id'), 'billing', ['stripe_checkout_session_id'], unique=False)
    op.create_index(op.f('ix_billing_stripe_customer_id'), 'billing', ['stripe_customer_id'], unique=False)
    op.create_index(op.f('ix_billing_stripe_payment_intent_id'), 'billing', ['stripe_payment_intent_id'], unique=False)
    op.drop_column('billing', 'paypal_payment_link')
    op.drop_column('billing', 'paypal_invoice_id')
    op.add_column('jobs', sa.Column('deltek_invoice_id', sa.String(), nullable=True))
    op.add_column('jobs', sa.Column('deltek_project_number', sa.String(), nullable=True))
    op.add_column('jobs', sa.Column('deltek_billing_client_id', sa.String(), nullable=True))
    op.add_column('jobs', sa.Column('deltek_original_amount', sa.String(), nullable=True))
    op.add_column('jobs', sa.Column('deltek_invoice_date', sa.DateTime(timezone=True), nullable=True))
    # Add is_deltek_import with default value first
    op.add_column('jobs', sa.Column('is_deltek_import', sa.Boolean(), nullable=True, default=False))
    # Update all existing records to have False value
    op.execute("UPDATE jobs SET is_deltek_import = FALSE WHERE is_deltek_import IS NULL")
    # Now make it NOT NULL
    op.alter_column('jobs', 'is_deltek_import', nullable=False)
    op.add_column('jobs', sa.Column('deltek_last_sync', sa.DateTime(timezone=True), nullable=True))
    op.create_index(op.f('ix_jobs_deltek_invoice_id'), 'jobs', ['deltek_invoice_id'], unique=False)
    op.create_index(op.f('ix_jobs_deltek_project_number'), 'jobs', ['deltek_project_number'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_jobs_deltek_project_number'), table_name='jobs')
    op.drop_index(op.f('ix_jobs_deltek_invoice_id'), table_name='jobs')
    op.drop_column('jobs', 'deltek_last_sync')
    op.drop_column('jobs', 'is_deltek_import')
    op.drop_column('jobs', 'deltek_invoice_date')
    op.drop_column('jobs', 'deltek_original_amount')
    op.drop_column('jobs', 'deltek_billing_client_id')
    op.drop_column('jobs', 'deltek_project_number')
    op.drop_column('jobs', 'deltek_invoice_id')
    op.add_column('billing', sa.Column('paypal_invoice_id', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('billing', sa.Column('paypal_payment_link', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.drop_index(op.f('ix_billing_stripe_payment_intent_id'), table_name='billing')
    op.drop_index(op.f('ix_billing_stripe_customer_id'), table_name='billing')
    op.drop_index(op.f('ix_billing_stripe_checkout_session_id'), table_name='billing')
    op.create_index('idx_billing_stripe_payment_intent_id', 'billing', ['stripe_payment_intent_id'], unique=False)
    op.create_index('idx_billing_stripe_customer_id', 'billing', ['stripe_customer_id'], unique=False)
    op.create_index('idx_billing_stripe_checkout_session_id', 'billing', ['stripe_checkout_session_id'], unique=False)
    op.create_index('idx_billing_payment_method_type', 'billing', ['payment_method_type'], unique=False)
    op.alter_column('billing', 'payment_method_type',
               existing_type=sa.VARCHAR(length=50),
               comment='Payment method type: paypal, stripe_ach, etc.',
               existing_nullable=True)
    op.alter_column('billing', 'stripe_customer_id',
               existing_type=sa.VARCHAR(length=255),
               comment='Stripe Customer ID for payment processing',
               existing_nullable=True)
    op.alter_column('billing', 'stripe_payment_intent_id',
               existing_type=sa.VARCHAR(length=255),
               comment='Stripe Payment Intent ID for ACH payments',
               existing_nullable=True)
    # ### end Alembic commands ###
